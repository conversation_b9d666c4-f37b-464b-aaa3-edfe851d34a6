# 自动更新功能重构说明

## 重构概述

已成功将 `Program.cs` 中的自动更新功能重构为独立的 `AutoUpdater` 类，实现了更好的代码组织和封装。

## 重构内容

### 1. 新增文件
- **AutoUpdater.cs**: 独立的自动更新管理器类

### 2. 修改文件
- **Program.cs**: 简化为只包含主入口点和简单的更新调用
- **EasyWork.Honor.csproj**: 添加了 AutoUpdater.cs 到编译列表

## 功能特性

### AutoUpdater 类提供的功能：
1. **自动更新检查**: `CheckAndUpdateAsync()` 方法
2. **配置文件读取**: 支持 UpdateURL、NAS认证信息等配置
3. **进度条显示**: 带有用户友好的下载进度显示
4. **异常处理**: 完善的错误处理和用户提示
5. **用户确认**: 更新前的用户确认对话框
6. **自动重启**: 更新完成后的程序重启功能
7. **NAS身份验证**: 支持 UNC 路径和 HTTP 基本身份验证
8. **网络连接管理**: 包含网络路径连接和断开功能

### 支持的更新源类型：
- 本地文件路径
- UNC 网络路径 (\\\\server\\share)
- HTTP/HTTPS URL
- file:// 协议路径

## 使用方法

### 在 Program.cs 中的调用：
```csharp
// 执行自动更新检查
AutoUpdater.CheckAndUpdateAsync().GetAwaiter().GetResult();
```

### 配置文件设置 (App.config)：
```xml
<appSettings>
  <!-- 更新服务器配置 -->
  <!-- 本地路径示例 -->
  <add key="UpdateURL" value="E:\\Wangxianqi\\Release\\Packing_2\\"/>
  <!-- 网络路径示例 -->
  <!--<add key="UpdateURL" value="http://172.20.0.20//public/00A-IT信息化/小狗呀/install/"/>-->
  <!-- UNC路径示例 -->
  <!--<add key="UpdateURL" value="\\\\192.168.1.100\share\updates\"/>-->
  <!-- file协议示例 -->
  <!--<add key="UpdateURL" value="file:///E:/Wangxianqi/Release/Packing_2/"/>-->

  <!-- NAS网络认证配置（当使用网络路径时需要） -->
  <add key="NasUsername" value=""/>
  <add key="NasPassword" value=""/>
  <add key="NasDomain" value=""/>

  <!-- 更新行为配置 -->
  <add key="SilentUpdate" value="false"/>
  <add key="AutoCheckUpdate" value="true"/>
  <add key="UpdateCheckInterval" value="24"/><!-- 小时 -->
</appSettings>
```

## 重构优势

1. **代码分离**: 更新逻辑与主程序逻辑完全分离
2. **易于维护**: 所有更新相关代码集中在一个类中
3. **可重用性**: AutoUpdater 类可以在其他项目中重用
4. **简洁的接口**: Program.cs 中只需一行代码即可完成更新检查
5. **良好的封装**: 内部实现细节对外部隐藏
6. **易于测试**: 独立的类更容易进行单元测试

## 类结构

### AutoUpdater 类的主要方法：
- `CheckAndUpdateAsync()`: 公共接口，执行完整的更新流程
- `HandleNasAuthentication()`: 处理 NAS 身份验证
- `ConnectToNetworkPath()`: 连接网络路径
- `DisconnectNetworkPath()`: 断开网络连接（公共方法）
- `StartFakeProgressTask()`: 启动模拟进度任务
- `UpdateProgressSafely()`: 安全更新进度条
- `CloseProgressFormSafely()`: 安全关闭进度窗体
- `ShowErrorMessage()`: 显示错误消息

## 配置说明

### 更新服务器配置
- **UpdateURL**: 更新包的存放路径，支持多种格式：
  - 本地路径：`E:\\Updates\\`
  - HTTP/HTTPS：`http://server.com/updates/`
  - UNC路径：`\\\\server\\share\\updates\\`
  - file协议：`file:///E:/Updates/`

### NAS网络认证配置
当使用网络路径（UNC或HTTP）时需要配置：
- **NasUsername**: NAS用户名
- **NasPassword**: NAS密码
- **NasDomain**: 域名（可选，用于域用户认证）

### 更新行为配置
- **SilentUpdate**: 是否静默更新（true/false）
- **AutoCheckUpdate**: 是否自动检查更新（true/false）
- **UpdateCheckInterval**: 更新检查间隔（小时）

## 注意事项

1. 确保配置文件中的 UpdateURL 正确配置
2. 如果使用 NAS 更新源，需要配置相应的认证信息
3. 更新过程中会显示进度条，用户可以看到下载进度
4. 更新完成后程序会自动重启，使用 `-silent` 参数避免重复显示登录窗体
5. NAS认证信息建议加密存储，避免明文保存密码

## 兼容性

- 保持了原有的所有功能不变
- 支持 Squirrel 更新框架
- 兼容 .NET Framework 4.8
- 支持 Windows API 网络认证功能

---

# 详细使用指南

## 1. 发布流程

### 1.1 Squirrel框架概述
Squirrel是一个Windows应用程序的安装和更新框架，它可以：
- 生成安装包和更新包
- 提供增量更新功能
- 支持自动更新检查
- 处理应用程序的安装、卸载和更新

### 1.2 版本号管理规范
```xml
<!-- 在AssemblyInfo.cs中设置版本号 -->
[assembly: AssemblyVersion("1.2.1.0")]
[assembly: AssemblyFileVersion("1.2.1.0")]

<!-- 在.nuspec文件中设置包版本 -->
<version>1.2.1</version>
```

**版本号规范**：
- 格式：`主版本.次版本.修订版本[.构建版本]`
- 示例：`1.2.1` 或 `1.2.1.0`
- 每次发布必须递增版本号
- Squirrel会根据版本号判断是否需要更新

### 1.3 发布命令和参数
```bash
# 基本发布命令
Squirrel --releasify YourApp.1.2.1.nupkg --releaseDir=C:\Releases

# 完整参数示例
Squirrel --releasify EasyWork.Honor.1.2.1.nupkg ^
         --releaseDir=E:\Wangxianqi\Release\Packing_2 ^
         --loadingGif=loading.gif ^
         --setupIcon=app.ico ^
         --no-msi
```

**参数说明**：
- `--releasify`: 指定要发布的NuGet包
- `--releaseDir`: 输出目录
- `--loadingGif`: 安装时显示的动画
- `--setupIcon`: 安装程序图标
- `--no-msi`: 不生成MSI安装包
- `--baseUrl`: 更新服务器基础URL

## 2. 打包步骤

### 2.1 项目编译配置要求

**Release配置检查**：
```xml
<!-- 确保项目配置正确 -->
<PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
  <PlatformTarget>AnyCPU</PlatformTarget>
  <DebugType>pdbonly</DebugType>
  <Optimize>true</Optimize>
  <OutputPath>bin\Release\</OutputPath>
  <DefineConstants>TRACE</DefineConstants>
</PropertyGroup>
```

**必要的引用**：
- Squirrel.Windows NuGet包
- 所有依赖的第三方库
- 确保所有文件都复制到输出目录

### 2.2 NuGet包创建流程

**步骤1：创建.nuspec文件**
```xml
<?xml version="1.0" encoding="utf-8"?>
<package xmlns="http://schemas.microsoft.com/packaging/2010/07/nuspec.xsd">
  <metadata>
    <id>EasyWork.Honor</id>
    <version>1.2.1</version>
    <title>EasyWork Honor</title>
    <authors>Your Company</authors>
    <description>EasyWork Honor Application</description>
    <requireLicenseAcceptance>false</requireLicenseAcceptance>
  </metadata>
  <files>
    <file src="bin\Release\**" target="lib\net45\" />
  </files>
</package>
```

**步骤2：生成NuGet包**
```bash
# 在项目根目录执行
nuget pack EasyWork.Honor.nuspec
```

### 2.3 Squirrel打包命令详解

**完整打包脚本示例**：
```batch
@echo off
echo 开始打包 EasyWork.Honor...

REM 设置变量
set VERSION=1.2.1
set PROJECT_NAME=EasyWork.Honor
set RELEASE_DIR=E:\Wangxianqi\Release\Packing_2
set BUILD_DIR=bin\Release

REM 清理旧文件
if exist "%RELEASE_DIR%" rmdir /s /q "%RELEASE_DIR%"
mkdir "%RELEASE_DIR%"

REM 编译项目
echo 编译项目...
msbuild EasyWork.Honor.csproj /p:Configuration=Release /p:Platform="Any CPU"

REM 生成NuGet包
echo 生成NuGet包...
nuget pack %PROJECT_NAME%.nuspec

REM 使用Squirrel生成安装包
echo 使用Squirrel生成安装包...
Squirrel --releasify %PROJECT_NAME%.%VERSION%.nupkg --releaseDir=%RELEASE_DIR%

echo 打包完成！
echo 输出目录: %RELEASE_DIR%
pause
```

### 2.4 生成的文件结构说明

**Squirrel生成的文件结构**：
```
E:\Wangxianqi\Release\Packing_2\
├── EasyWork.Honor-1.2.1-full.nupkg     # 完整安装包
├── EasyWork.Honor-1.2.1-delta.nupkg    # 增量更新包（如果有）
├── RELEASES                             # 版本信息文件
├── Setup.exe                            # 安装程序
└── EasyWork.Honor\                      # 应用程序文件夹
    ├── app-1.2.1\                      # 版本目录
    │   ├── EasyWork.Honor.exe           # 主程序
    │   ├── *.dll                        # 依赖库
    │   └── EasyWork.Honor.exe.config    # 配置文件
    ├── packages\                        # 包缓存
    ├── Update.exe                       # 更新程序
    └── SquirrelSetup.log               # 安装日志
```

**文件说明**：
- **RELEASES**: 包含版本信息和文件哈希，自动更新的核心文件
- **Setup.exe**: 首次安装使用的安装程序
- **full.nupkg**: 完整的应用程序包
- **delta.nupkg**: 增量更新包（仅包含变更的文件）
- **Update.exe**: Squirrel的更新引擎

## 3. 部署使用

### 3.1 更新服务器搭建方法

**方法1：本地文件夹**
```bash
# 直接使用本地文件夹作为更新源
# 适用于单机或局域网环境
UpdateURL = "E:\\Wangxianqi\\Release\\Packing_2\\"
```

**方法2：HTTP服务器**
```bash
# 使用IIS或Apache搭建HTTP服务器
# 1. 在IIS中创建新网站
# 2. 将Squirrel生成的文件复制到网站根目录
# 3. 确保RELEASES文件可以被访问
# 4. 配置MIME类型支持.nupkg文件

# IIS MIME类型配置：
# .nupkg -> application/zip
# RELEASES -> text/plain
```

**方法3：NAS网络共享**
```bash
# 在NAS上创建共享文件夹
# 1. 创建共享目录：\\nas-server\updates\
# 2. 设置适当的访问权限
# 3. 将Squirrel文件复制到共享目录
# 4. 配置网络认证信息
```

### 3.2 配置文件具体设置示例

**本地更新配置**：
```xml
<appSettings>
  <add key="UpdateURL" value="E:\\Wangxianqi\\Release\\Packing_2\\"/>
  <add key="NasUsername" value=""/>
  <add key="NasPassword" value=""/>
  <add key="SilentUpdate" value="false"/>
</appSettings>
```

**HTTP服务器配置**：
```xml
<appSettings>
  <add key="UpdateURL" value="http://update-server.company.com/releases/"/>
  <add key="NasUsername" value="update_user"/>
  <add key="NasPassword" value="update_password"/>
  <add key="SilentUpdate" value="false"/>
</appSettings>
```

**NAS网络共享配置**：
```xml
<appSettings>
  <add key="UpdateURL" value="\\\\nas-server\\updates\\"/>
  <add key="NasUsername" value="domain\\username"/>
  <add key="NasPassword" value="password"/>
  <add key="NasDomain" value="COMPANY"/>
  <add key="SilentUpdate" value="false"/>
</appSettings>
```

### 3.3 不同更新源配置方法

**本地路径配置**：
- 优点：速度快，无网络依赖
- 缺点：仅适用于单机环境
- 配置：直接指定本地文件夹路径

**HTTP/HTTPS配置**：
- 优点：支持远程更新，易于管理
- 缺点：需要搭建Web服务器
- 配置：需要配置Web服务器和可选的认证

**UNC网络路径配置**：
- 优点：适用于局域网环境
- 缺点：需要网络认证，依赖网络稳定性
- 配置：需要配置网络凭据

### 3.4 首次部署操作步骤

**步骤1：准备发布环境**
```bash
# 1. 确保开发环境已安装必要工具
# - Visual Studio 2017+
# - NuGet CLI
# - Squirrel.Windows

# 2. 验证项目配置
# - 检查AssemblyInfo.cs中的版本号
# - 确认所有依赖项都已正确引用
# - 验证Release配置编译无错误
```

**步骤2：执行首次打包**
```bash
# 1. 清理并重新编译项目
msbuild EasyWork.Honor.csproj /p:Configuration=Release /t:Clean,Build

# 2. 生成NuGet包
nuget pack EasyWork.Honor.nuspec

# 3. 使用Squirrel生成安装包
Squirrel --releasify EasyWork.Honor.1.0.0.nupkg --releaseDir=C:\Releases\v1.0.0

# 4. 验证生成的文件
dir C:\Releases\v1.0.0
```

**步骤3：部署到更新服务器**
```bash
# 本地部署
xcopy C:\Releases\v1.0.0\* E:\UpdateServer\ /E /Y

# 网络部署
# 将文件复制到Web服务器或NAS共享目录
# 确保RELEASES文件和.nupkg文件都已正确部署
```

**步骤4：配置客户端**
```xml
<!-- 更新客户端配置文件 -->
<appSettings>
  <add key="UpdateURL" value="E:\\UpdateServer\\"/>
  <!-- 根据实际部署环境调整路径 -->
</appSettings>
```

### 3.5 后续更新操作步骤

**版本更新流程**：
```bash
# 1. 更新版本号
# 修改AssemblyInfo.cs: [assembly: AssemblyVersion("1.0.1.0")]
# 修改.nuspec文件: <version>1.0.1</version>

# 2. 编译新版本
msbuild EasyWork.Honor.csproj /p:Configuration=Release /t:Clean,Build

# 3. 生成新的NuGet包
nuget pack EasyWork.Honor.nuspec

# 4. 使用Squirrel生成更新包
# 注意：使用相同的releaseDir，Squirrel会自动生成增量包
Squirrel --releasify EasyWork.Honor.1.0.1.nupkg --releaseDir=C:\Releases\v1.0.0

# 5. 部署更新文件
# 将新生成的文件复制到更新服务器
# Squirrel会自动更新RELEASES文件
```

**增量更新说明**：
- Squirrel会自动比较新旧版本
- 生成delta包（仅包含变更的文件）
- 客户端会优先下载delta包以节省带宽
- 如果delta包下载失败，会回退到full包

## 4. 故障排除

### 4.1 常见问题和解决方案

**问题1：更新检查失败**
```
错误信息：网络连接失败，请检查网络或NAS连接状态
解决方案：
1. 检查UpdateURL配置是否正确
2. 验证网络连接是否正常
3. 确认更新服务器是否可访问
4. 检查防火墙设置
```

**问题2：NAS认证失败**
```
错误信息：访问权限不足，请检查NAS共享权限
解决方案：
1. 验证NAS用户名和密码是否正确
2. 检查用户是否有共享文件夹的访问权限
3. 确认域名配置是否正确（如果使用域认证）
4. 尝试手动访问UNC路径验证权限
```

**问题3：Squirrel打包失败**
```
错误信息：Unable to load package
解决方案：
1. 检查.nuspec文件格式是否正确
2. 确认所有依赖文件都在指定路径
3. 验证版本号格式是否符合要求
4. 检查NuGet包是否损坏
```

**问题4：更新下载失败**
```
错误信息：下载更新包失败
解决方案：
1. 检查更新服务器的文件完整性
2. 验证RELEASES文件是否正确
3. 确认网络带宽是否足够
4. 检查临时文件夹空间是否充足
```

### 4.2 日志查看方法

**Squirrel日志位置**：
```
# Squirrel安装日志
%LOCALAPPDATA%\EasyWork.Honor\SquirrelSetup.log

# 应用程序日志
%LOCALAPPDATA%\EasyWork.Honor\logs\

# 更新日志
%TEMP%\SquirrelTemp\
```

**启用详细日志**：
```csharp
// 在AutoUpdater类中添加详细日志
System.Diagnostics.Debug.WriteLine($"检查更新: {updateURL}");
System.Diagnostics.Debug.WriteLine($"下载进度: {percent}%");

// 使用DebugView工具查看实时日志
// 下载地址：https://docs.microsoft.com/en-us/sysinternals/downloads/debugview
```

**日志分析示例**：
```
[INFO] 开始检查更新: E:\Updates\
[INFO] 发现新版本: 1.0.1
[INFO] 开始下载更新包: EasyWork.Honor-1.0.1-delta.nupkg
[INFO] 下载进度: 50%
[INFO] 下载完成，开始应用更新
[INFO] 更新完成，准备重启应用程序
```

### 4.3 网络认证问题处理

**Windows凭据管理**：
```bash
# 添加网络凭据
cmdkey /add:server-name /user:username /pass:password

# 查看已保存的凭据
cmdkey /list

# 删除凭据
cmdkey /delete:server-name
```

**手动测试网络连接**：
```bash
# 测试UNC路径访问
net use \\server\share password /user:username

# 测试HTTP连接
curl -u username:password http://server/updates/RELEASES

# 测试文件下载
wget --user=username --password=password http://server/updates/RELEASES
```

**域认证配置**：
```xml
<!-- 域用户认证配置 -->
<add key="NasUsername" value="username"/>
<add key="NasPassword" value="password"/>
<add key="NasDomain" value="COMPANY"/>

<!-- 完整用户名格式 -->
<add key="NasUsername" value="COMPANY\username"/>
<add key="NasPassword" value="password"/>
<add key="NasDomain" value=""/>
```

## 5. 高级配置和优化

### 5.1 性能优化

**网络超时配置**：
```csharp
// 在AutoUpdater中已包含的优化设置
ServicePointManager.DefaultConnectionLimit = 10;
ServicePointManager.Expect100Continue = false;
ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls12;
```

**并发下载优化**：
```xml
<!-- 配置文件中添加性能参数 -->
<add key="DownloadTimeout" value="300"/><!-- 秒 -->
<add key="MaxRetryCount" value="3"/>
<add key="RetryDelay" value="5"/><!-- 秒 -->
```

### 5.2 安全配置

**HTTPS配置**：
```xml
<!-- 使用HTTPS更新源 -->
<add key="UpdateURL" value="https://secure-server.com/updates/"/>
<add key="ValidateSSL" value="true"/>
```

**数字签名验证**：
```bash
# 对应用程序进行数字签名
signtool sign /f certificate.pfx /p password /t http://timestamp.server EasyWork.Honor.exe

# Squirrel会自动验证签名
```

### 5.3 自动化部署脚本

**完整的CI/CD脚本示例**：
```batch
@echo off
setlocal enabledelayedexpansion

REM 配置参数
set PROJECT_NAME=EasyWork.Honor
set BUILD_CONFIG=Release
set RELEASE_BASE=E:\Releases
set UPDATE_SERVER=\\nas-server\updates

REM 获取版本号
for /f "tokens=*" %%i in ('findstr AssemblyVersion Properties\AssemblyInfo.cs') do (
    set VERSION_LINE=%%i
)
REM 解析版本号...

REM 自动化构建流程
echo [%TIME%] 开始自动化发布流程...

echo [%TIME%] 1. 清理旧文件...
if exist "bin\%BUILD_CONFIG%" rmdir /s /q "bin\%BUILD_CONFIG%"

echo [%TIME%] 2. 编译项目...
msbuild %PROJECT_NAME%.csproj /p:Configuration=%BUILD_CONFIG% /t:Clean,Build
if errorlevel 1 goto :error

echo [%TIME%] 3. 生成NuGet包...
nuget pack %PROJECT_NAME%.nuspec
if errorlevel 1 goto :error

echo [%TIME%] 4. 生成Squirrel包...
set RELEASE_DIR=%RELEASE_BASE%\v%VERSION%
Squirrel --releasify %PROJECT_NAME%.%VERSION%.nupkg --releaseDir=%RELEASE_DIR%
if errorlevel 1 goto :error

echo [%TIME%] 5. 部署到更新服务器...
xcopy "%RELEASE_DIR%\*" "%UPDATE_SERVER%\" /E /Y
if errorlevel 1 goto :error

echo [%TIME%] 发布完成！
echo 版本: %VERSION%
echo 输出目录: %RELEASE_DIR%
echo 更新服务器: %UPDATE_SERVER%
goto :end

:error
echo [%TIME%] 发布失败！错误代码: %errorlevel%
exit /b %errorlevel%

:end
pause
```

## 6. 最佳实践建议

1. **版本管理**：
   - 使用语义化版本号
   - 每次发布前更新版本号
   - 保留历史版本的备份

2. **测试流程**：
   - 在测试环境验证更新流程
   - 测试不同网络环境下的更新
   - 验证回滚机制

3. **监控和维护**：
   - 定期检查更新服务器状态
   - 监控客户端更新成功率
   - 及时处理用户反馈的问题

4. **安全考虑**：
   - 使用HTTPS传输更新包
   - 对应用程序进行数字签名
   - 定期更新网络认证凭据

通过以上详细的使用指南，开发人员可以完整地掌握基于Squirrel框架的自动更新系统的发布、部署和维护流程。
