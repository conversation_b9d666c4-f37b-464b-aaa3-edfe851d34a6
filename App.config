<?xml version="1.0" encoding="utf-8"?>
<configuration>
    <startup> 
        <supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.8"/>
    </startup>
  <runtime>
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <dependentAssembly>
        <assemblyIdentity name="System.Text.Encoding.CodePages" publicKeyToken="b03f5f7f11d50a3a" culture="neutral"/>
        <bindingRedirect oldVersion="0.0.0.0-4.1.1.0" newVersion="4.1.1.0"/>
      </dependentAssembly>
    </assemblyBinding>
  </runtime>
  <appSettings>
    <!-- 更新服务器配置 -->
    <!-- 本地路径示例 -->
    <add key="UpdateURL" value="E:\\Wangxianqi\\Release\\Packing_2\\"/>
    <!-- 网络路径示例 -->
    <!--<add key="UpdateURL" value="http://172.20.0.20//public/00A-IT信息化/小狗呀/install/"/>-->
    <!-- UNC路径示例 -->
    <!--<add key="UpdateURL" value="\\\\192.168.1.100\share\updates\"/>-->
    <!-- file协议示例 -->
    <!--<add key="UpdateURL" value="file:///E:/Wangxianqi/Release/Packing_2/"/>-->

    <!-- NAS网络认证配置（当使用网络路径时需要） -->
    <add key="NasUsername" value=""/>
    <add key="NasPassword" value=""/>
    <add key="NasDomain" value=""/>

    <!-- 更新行为配置 -->
    <add key="SilentUpdate" value="false"/>
    <add key="AutoCheckUpdate" value="true"/>
    <add key="UpdateCheckInterval" value="24"/><!-- 小时 -->
  </appSettings>
</configuration>
