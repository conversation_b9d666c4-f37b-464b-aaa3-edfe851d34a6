using System;
using System.Threading.Tasks;
using System.Windows.Forms;
using EasyWork.Honor.Service.Public;

namespace EasyWork.Honor
{
    public partial class UpdateTestForm : Form
    {
        private Button btnTestConnection;
        private Button btnTestUpdate;
        private TextBox txtLog;
        private Label lblStatus;

        public UpdateTestForm()
        {
            InitializeComponent();
        }

        private void InitializeComponent()
        {
            this.btnTestConnection = new Button();
            this.btnTestUpdate = new Button();
            this.txtLog = new TextBox();
            this.lblStatus = new Label();
            this.SuspendLayout();

            // btnTestConnection
            this.btnTestConnection.Location = new System.Drawing.Point(12, 12);
            this.btnTestConnection.Name = "btnTestConnection";
            this.btnTestConnection.Size = new System.Drawing.Size(120, 30);
            this.btnTestConnection.Text = "测试连接";
            this.btnTestConnection.UseVisualStyleBackColor = true;
            this.btnTestConnection.Click += new EventHandler(this.btnTestConnection_Click);

            // btnTestUpdate
            this.btnTestUpdate.Location = new System.Drawing.Point(150, 12);
            this.btnTestUpdate.Name = "btnTestUpdate";
            this.btnTestUpdate.Size = new System.Drawing.Size(120, 30);
            this.btnTestUpdate.Text = "测试更新";
            this.btnTestUpdate.UseVisualStyleBackColor = true;
            this.btnTestUpdate.Click += new EventHandler(this.btnTestUpdate_Click);

            // lblStatus
            this.lblStatus.Location = new System.Drawing.Point(12, 50);
            this.lblStatus.Name = "lblStatus";
            this.lblStatus.Size = new System.Drawing.Size(560, 20);
            this.lblStatus.Text = "准备就绪";

            // txtLog
            this.txtLog.Location = new System.Drawing.Point(12, 80);
            this.txtLog.Multiline = true;
            this.txtLog.Name = "txtLog";
            this.txtLog.ScrollBars = ScrollBars.Vertical;
            this.txtLog.Size = new System.Drawing.Size(560, 300);
            this.txtLog.ReadOnly = true;

            // UpdateTestForm
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(584, 391);
            this.Controls.Add(this.txtLog);
            this.Controls.Add(this.lblStatus);
            this.Controls.Add(this.btnTestUpdate);
            this.Controls.Add(this.btnTestConnection);
            this.Name = "UpdateTestForm";
            this.Text = "自动更新测试工具";
            this.ResumeLayout(false);
            this.PerformLayout();
        }

        private async void btnTestConnection_Click(object sender, EventArgs e)
        {
            lblStatus.Text = "正在测试连接...";
            txtLog.Clear();
            btnTestConnection.Enabled = false;

            try
            {
                LogMessage("开始测试更新源连接...");
                bool result = await AppUpdate.TestUpdateSourceAsync();
                
                if (result)
                {
                    lblStatus.Text = "连接测试成功";
                    LogMessage("✓ 连接测试成功！更新源可以正常访问。");
                }
                else
                {
                    lblStatus.Text = "连接测试失败";
                    LogMessage("✗ 连接测试失败！请检查配置和网络连接。");
                }
            }
            catch (Exception ex)
            {
                lblStatus.Text = "连接测试异常";
                LogMessage($"✗ 连接测试异常: {ex.Message}");
            }
            finally
            {
                btnTestConnection.Enabled = true;
            }
        }

        private async void btnTestUpdate_Click(object sender, EventArgs e)
        {
            lblStatus.Text = "正在测试更新...";
            txtLog.Clear();
            btnTestUpdate.Enabled = false;

            try
            {
                LogMessage("开始测试完整更新流程...");
                await AppUpdate.CheckAndUpdateAsync();
                lblStatus.Text = "更新测试完成";
                LogMessage("✓ 更新测试完成");
            }
            catch (Exception ex)
            {
                lblStatus.Text = "更新测试失败";
                LogMessage($"✗ 更新测试失败: {ex.Message}");
                LogMessage($"详细错误: {ex.StackTrace}");
            }
            finally
            {
                btnTestUpdate.Enabled = true;
            }
        }

        private void LogMessage(string message)
        {
            if (txtLog.InvokeRequired)
            {
                txtLog.Invoke(new Action(() => LogMessage(message)));
                return;
            }

            string timestamp = DateTime.Now.ToString("HH:mm:ss");
            txtLog.AppendText($"[{timestamp}] {message}\r\n");
            txtLog.ScrollToCaret();
        }
    }
}
