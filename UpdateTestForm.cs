using System;
using System.Threading.Tasks;
using System.Windows.Forms;
using EasyWork.Honor.Service;

namespace EasyWork.Honor
{
    public partial class UpdateTestForm : Form
    {
        private Button btnTestConnection;
        private Button btnTestUpdate;
        private Button btnCheckReleases;
        private Button btnListFiles;
        private Button btnGenerateReleases;
        private TextBox txtLog;
        private Label lblStatus;

        public UpdateTestForm()
        {
            InitializeComponent();
        }

        private void InitializeComponent()
        {
            this.btnTestConnection = new Button();
            this.btnTestUpdate = new Button();
            this.btnCheckReleases = new Button();
            this.btnListFiles = new Button();
            this.btnGenerateReleases = new Button();
            this.txtLog = new TextBox();
            this.lblStatus = new Label();
            this.SuspendLayout();

            // btnTestConnection
            this.btnTestConnection.Location = new System.Drawing.Point(12, 12);
            this.btnTestConnection.Name = "btnTestConnection";
            this.btnTestConnection.Size = new System.Drawing.Size(100, 30);
            this.btnTestConnection.Text = "测试连接";
            this.btnTestConnection.UseVisualStyleBackColor = true;
            this.btnTestConnection.Click += new EventHandler(this.btnTestConnection_Click);

            // btnCheckReleases
            this.btnCheckReleases.Location = new System.Drawing.Point(120, 12);
            this.btnCheckReleases.Name = "btnCheckReleases";
            this.btnCheckReleases.Size = new System.Drawing.Size(100, 30);
            this.btnCheckReleases.Text = "检查RELEASES";
            this.btnCheckReleases.UseVisualStyleBackColor = true;
            this.btnCheckReleases.Click += new EventHandler(this.btnCheckReleases_Click);

            // btnListFiles
            this.btnListFiles.Location = new System.Drawing.Point(228, 12);
            this.btnListFiles.Name = "btnListFiles";
            this.btnListFiles.Size = new System.Drawing.Size(100, 30);
            this.btnListFiles.Text = "列出文件";
            this.btnListFiles.UseVisualStyleBackColor = true;
            this.btnListFiles.Click += new EventHandler(this.btnListFiles_Click);

            // btnGenerateReleases
            this.btnGenerateReleases.Location = new System.Drawing.Point(336, 12);
            this.btnGenerateReleases.Name = "btnGenerateReleases";
            this.btnGenerateReleases.Size = new System.Drawing.Size(100, 30);
            this.btnGenerateReleases.Text = "生成RELEASES";
            this.btnGenerateReleases.UseVisualStyleBackColor = true;
            this.btnGenerateReleases.Click += new EventHandler(this.btnGenerateReleases_Click);

            // btnTestUpdate
            this.btnTestUpdate.Location = new System.Drawing.Point(444, 12);
            this.btnTestUpdate.Name = "btnTestUpdate";
            this.btnTestUpdate.Size = new System.Drawing.Size(100, 30);
            this.btnTestUpdate.Text = "测试更新";
            this.btnTestUpdate.UseVisualStyleBackColor = true;
            this.btnTestUpdate.Click += new EventHandler(this.btnTestUpdate_Click);

            // lblStatus
            this.lblStatus.Location = new System.Drawing.Point(12, 50);
            this.lblStatus.Name = "lblStatus";
            this.lblStatus.Size = new System.Drawing.Size(560, 20);
            this.lblStatus.Text = "准备就绪";

            // txtLog
            this.txtLog.Location = new System.Drawing.Point(12, 80);
            this.txtLog.Multiline = true;
            this.txtLog.Name = "txtLog";
            this.txtLog.ScrollBars = ScrollBars.Vertical;
            this.txtLog.Size = new System.Drawing.Size(560, 300);
            this.txtLog.ReadOnly = true;

            // UpdateTestForm
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(584, 391);
            this.Controls.Add(this.txtLog);
            this.Controls.Add(this.lblStatus);
            this.Controls.Add(this.btnTestUpdate);
            this.Controls.Add(this.btnGenerateReleases);
            this.Controls.Add(this.btnListFiles);
            this.Controls.Add(this.btnCheckReleases);
            this.Controls.Add(this.btnTestConnection);
            this.Name = "UpdateTestForm";
            this.Text = "自动更新测试工具 - RELEASES诊断版";
            this.ResumeLayout(false);
            this.PerformLayout();
        }

        private async void btnTestConnection_Click(object sender, EventArgs e)
        {
            lblStatus.Text = "正在测试连接...";
            txtLog.Clear();
            btnTestConnection.Enabled = false;

            try
            {
                LogMessage("开始测试更新源连接...");
                bool result = await AppUpdate.TestUpdateSourceAsync();
                
                if (result)
                {
                    lblStatus.Text = "连接测试成功";
                    LogMessage("✓ 连接测试成功！更新源可以正常访问。");
                }
                else
                {
                    lblStatus.Text = "连接测试失败";
                    LogMessage("✗ 连接测试失败！请检查配置和网络连接。");
                }
            }
            catch (Exception ex)
            {
                lblStatus.Text = "连接测试异常";
                LogMessage($"✗ 连接测试异常: {ex.Message}");
            }
            finally
            {
                btnTestConnection.Enabled = true;
            }
        }

        private async void btnTestUpdate_Click(object sender, EventArgs e)
        {
            lblStatus.Text = "正在测试更新...";
            txtLog.Clear();
            btnTestUpdate.Enabled = false;

            try
            {
                LogMessage("开始测试完整更新流程...");
                await AppUpdate.CheckAndUpdateAsync();
                lblStatus.Text = "更新测试完成";
                LogMessage("✓ 更新测试完成");
            }
            catch (Exception ex)
            {
                lblStatus.Text = "更新测试失败";
                LogMessage($"✗ 更新测试失败: {ex.Message}");
                LogMessage($"详细错误: {ex.StackTrace}");
            }
            finally
            {
                btnTestUpdate.Enabled = true;
            }
        }

        private async void btnCheckReleases_Click(object sender, EventArgs e)
        {
            lblStatus.Text = "正在检查RELEASES文件...";
            txtLog.Clear();
            btnCheckReleases.Enabled = false;

            try
            {
                LogMessage("开始检查RELEASES文件...");

                var updateURL = System.Configuration.ConfigurationManager.AppSettings["UpdateURL"];
                if (string.IsNullOrEmpty(updateURL))
                {
                    LogMessage("❌ UpdateURL未配置");
                    return;
                }

                LogMessage($"更新源: {updateURL}");

                if (updateURL.StartsWith("\\\\"))
                {
                    // UNC路径
                    string releasesPath = System.IO.Path.Combine(updateURL, "RELEASES");
                    LogMessage($"RELEASES文件路径: {releasesPath}");

                    if (!System.IO.File.Exists(releasesPath))
                    {
                        LogMessage("❌ RELEASES文件不存在！");
                        lblStatus.Text = "RELEASES文件不存在";
                        return;
                    }

                    // 读取并分析文件
                    var fileInfo = new System.IO.FileInfo(releasesPath);
                    LogMessage($"文件大小: {fileInfo.Length} 字节");
                    LogMessage($"最后修改: {fileInfo.LastWriteTime}");

                    if (fileInfo.Length == 0)
                    {
                        LogMessage("❌ RELEASES文件为空！");
                        lblStatus.Text = "RELEASES文件为空";
                        return;
                    }

                    string content = System.IO.File.ReadAllText(releasesPath);
                    AnalyzeReleasesContent(content);
                }

                lblStatus.Text = "RELEASES文件检查完成";
            }
            catch (Exception ex)
            {
                lblStatus.Text = "RELEASES文件检查失败";
                LogMessage($"❌ 检查RELEASES文件异常: {ex.Message}");
            }
            finally
            {
                btnCheckReleases.Enabled = true;
            }
        }

        private async void btnListFiles_Click(object sender, EventArgs e)
        {
            lblStatus.Text = "正在列出更新目录文件...";
            txtLog.Clear();
            btnListFiles.Enabled = false;

            try
            {
                LogMessage("开始列出更新目录文件...");

                var updateURL = System.Configuration.ConfigurationManager.AppSettings["UpdateURL"];
                if (string.IsNullOrEmpty(updateURL))
                {
                    LogMessage("❌ UpdateURL未配置");
                    return;
                }

                if (updateURL.StartsWith("\\\\"))
                {
                    if (!System.IO.Directory.Exists(updateURL))
                    {
                        LogMessage("❌ 更新目录不存在！");
                        return;
                    }

                    LogMessage($"更新目录: {updateURL}");
                    LogMessage("目录内容:");

                    var files = System.IO.Directory.GetFiles(updateURL);
                    var dirs = System.IO.Directory.GetDirectories(updateURL);

                    LogMessage($"📁 子目录 ({dirs.Length}个):");
                    foreach (var dir in dirs)
                    {
                        var dirInfo = new System.IO.DirectoryInfo(dir);
                        LogMessage($"  📁 {dirInfo.Name}");
                    }

                    LogMessage($"📄 文件 ({files.Length}个):");
                    foreach (var file in files)
                    {
                        var fileInfo = new System.IO.FileInfo(file);
                        LogMessage($"  📄 {fileInfo.Name} ({fileInfo.Length} 字节, {fileInfo.LastWriteTime})");

                        // 特别标注重要文件
                        if (fileInfo.Name.Equals("RELEASES", StringComparison.OrdinalIgnoreCase))
                        {
                            LogMessage($"    ✅ 找到RELEASES文件");
                        }
                        else if (fileInfo.Name.EndsWith(".nupkg", StringComparison.OrdinalIgnoreCase))
                        {
                            LogMessage($"    📦 NuGet包文件");
                        }
                        else if (fileInfo.Name.Equals("Setup.exe", StringComparison.OrdinalIgnoreCase))
                        {
                            LogMessage($"    🔧 安装程序");
                        }
                    }
                }

                lblStatus.Text = "文件列表完成";
            }
            catch (Exception ex)
            {
                lblStatus.Text = "列出文件失败";
                LogMessage($"❌ 列出文件异常: {ex.Message}");
            }
            finally
            {
                btnListFiles.Enabled = true;
            }
        }

        private void AnalyzeReleasesContent(string content)
        {
            LogMessage("=== RELEASES文件内容分析 ===");
            LogMessage($"内容长度: {content.Length} 字符");

            if (string.IsNullOrWhiteSpace(content))
            {
                LogMessage("❌ 文件内容为空或只包含空白字符");
                return;
            }

            // 显示原始内容
            LogMessage("原始内容:");
            LogMessage($"'{content}'");

            // 分析行
            var lines = content.Split(new[] { '\r', '\n' }, StringSplitOptions.RemoveEmptyEntries);
            LogMessage($"有效行数: {lines.Length}");

            if (lines.Length == 0)
            {
                LogMessage("❌ 没有有效的行");
                return;
            }

            for (int i = 0; i < lines.Length; i++)
            {
                var line = lines[i].Trim();
                LogMessage($"第{i + 1}行: '{line}'");

                if (string.IsNullOrEmpty(line))
                {
                    LogMessage("  ⚠️ 空行");
                    continue;
                }

                // 分析格式: SHA1 FILENAME SIZE
                var parts = line.Split(new[] { ' ', '\t' }, StringSplitOptions.RemoveEmptyEntries);
                LogMessage($"  分段数: {parts.Length}");

                if (parts.Length < 3)
                {
                    LogMessage($"  ❌ 格式错误: 应该至少有3个部分 (SHA1 文件名 大小)");
                    continue;
                }

                string sha1 = parts[0];
                string filename = parts[1];
                string sizeStr = parts[2];

                LogMessage($"  SHA1: {sha1} (长度: {sha1.Length})");
                LogMessage($"  文件名: {filename}");
                LogMessage($"  大小: {sizeStr}");

                // 验证SHA1
                if (sha1.Length != 40)
                {
                    LogMessage($"  ❌ SHA1长度错误: 应该是40个字符，实际{sha1.Length}个");
                }
                else if (!System.Text.RegularExpressions.Regex.IsMatch(sha1, "^[a-fA-F0-9]+$"))
                {
                    LogMessage($"  ❌ SHA1格式错误: 应该只包含十六进制字符");
                }
                else
                {
                    LogMessage($"  ✅ SHA1格式正确");
                }

                // 验证大小
                if (long.TryParse(sizeStr, out long size))
                {
                    LogMessage($"  ✅ 文件大小格式正确: {size} 字节");
                }
                else
                {
                    LogMessage($"  ❌ 文件大小格式错误: 无法解析为数字");
                }
            }

            LogMessage("=== 分析完成 ===");
        }

        private async void btnGenerateReleases_Click(object sender, EventArgs e)
        {
            lblStatus.Text = "正在生成RELEASES文件...";
            txtLog.Clear();
            btnGenerateReleases.Enabled = false;

            try
            {
                LogMessage("开始生成RELEASES文件...");

                var updateURL = System.Configuration.ConfigurationManager.AppSettings["UpdateURL"];
                if (string.IsNullOrEmpty(updateURL))
                {
                    LogMessage("❌ UpdateURL未配置");
                    return;
                }

                if (!updateURL.StartsWith("\\\\"))
                {
                    LogMessage("❌ 当前只支持UNC路径的RELEASES文件生成");
                    return;
                }

                LogMessage($"更新目录: {updateURL}");

                if (!System.IO.Directory.Exists(updateURL))
                {
                    LogMessage("❌ 更新目录不存在！");
                    return;
                }

                // 查找.nupkg文件
                var nupkgFiles = System.IO.Directory.GetFiles(updateURL, "*.nupkg");
                LogMessage($"找到 {nupkgFiles.Length} 个.nupkg文件");

                if (nupkgFiles.Length == 0)
                {
                    LogMessage("❌ 目录中没有找到.nupkg文件");
                    return;
                }

                // 生成RELEASES文件
                bool success = ReleasesFileGenerator.GenerateReleasesFile(updateURL);

                if (success)
                {
                    LogMessage("✅ RELEASES文件生成成功！");

                    // 验证生成的文件
                    string releasesPath = System.IO.Path.Combine(updateURL, "RELEASES");
                    var validationResult = ReleasesFileGenerator.ValidateReleasesFile(releasesPath);

                    LogMessage($"验证结果: {(validationResult.IsValid ? "✅ 有效" : "❌ 无效")}");
                    LogMessage($"验证信息: {validationResult.ErrorMessage}");
                    LogMessage($"包含条目: {validationResult.Entries.Count} 个");

                    foreach (var entry in validationResult.Entries)
                    {
                        LogMessage($"  📦 {entry.FileName} ({entry.FileSize} 字节)");
                    }

                    lblStatus.Text = "RELEASES文件生成成功";
                }
                else
                {
                    LogMessage("❌ RELEASES文件生成失败");
                    lblStatus.Text = "RELEASES文件生成失败";
                }
            }
            catch (Exception ex)
            {
                lblStatus.Text = "生成RELEASES文件异常";
                LogMessage($"❌ 生成RELEASES文件异常: {ex.Message}");
            }
            finally
            {
                btnGenerateReleases.Enabled = true;
            }
        }

        private void LogMessage(string message)
        {
            if (txtLog.InvokeRequired)
            {
                txtLog.Invoke(new Action(() => LogMessage(message)));
                return;
            }

            string timestamp = DateTime.Now.ToString("HH:mm:ss");
            txtLog.AppendText($"[{timestamp}] {message}\r\n");
            txtLog.ScrollToCaret();
        }
    }
}
