@echo off
setlocal enabledelayedexpansion

REM ========================================
REM EasyWork.Honor One-Click Publish Script
REM ========================================

echo.
echo ===============================================================
echo                    EasyWork.Honor One-Click Publish Tool
echo                            Version 1.0.0
echo ===============================================================
echo.

REM 配置参数
set "PROJECT_NAME=EasyWork.Honor"
set "PROJECT_FILE=%PROJECT_NAME%.csproj"
set "NUSPEC_FILE=%PROJECT_NAME%.nuspec"
set "BUILD_CONFIG=Release"
set "PLATFORM=Any CPU"
set "NAS_PATH=\\***********\public\00A-IT信息化\小狗呀\install"
set "NAS_USER=read"
set "NAS_PASS=123456"

REM 工作目录
set "WORK_DIR=%~dp0"
set "BUILD_DIR=%WORK_DIR%bin\%BUILD_CONFIG%"
set "TEMP_DIR=%WORK_DIR%temp_publish"
set "LOG_FILE=%WORK_DIR%publish_log_%date:~0,4%%date:~5,2%%date:~8,2%_%time:~0,2%%time:~3,2%%time:~6,2%.txt"

REM 创建日志文件
echo [%date% %time%] 开始一键发布流程 > "%LOG_FILE%"

echo [1/8] Environment Check...
call :log "Starting environment check"

REM Check required tools
where msbuild >nul 2>&1
if errorlevel 1 (
    call :error "MSBuild not found, please install Visual Studio or Build Tools"
    goto :end
)

where nuget >nul 2>&1
if errorlevel 1 (
    call :error "NuGet CLI not found, please download and add to PATH"
    goto :end
)

where Squirrel >nul 2>&1
if errorlevel 1 (
    call :error "Squirrel not found, please install Squirrel.Windows"
    goto :end
)

REM Check project files
if not exist "%PROJECT_FILE%" (
    call :error "Project file %PROJECT_FILE% not found"
    goto :end
)

if not exist "%NUSPEC_FILE%" (
    call :error "NuSpec file %NUSPEC_FILE% not found"
    goto :end
)

call :success "Environment check completed"

echo [2/8] Version Processing...
call :log "Starting version processing"

REM Read current version from AssemblyInfo.cs
for /f "tokens=2 delims=[]" %%i in ('findstr "AssemblyVersion" Properties\AssemblyInfo.cs') do (
    set ASSEMBLY_VERSION=%%i
)
set ASSEMBLY_VERSION=%ASSEMBLY_VERSION:"=%
set ASSEMBLY_VERSION=%ASSEMBLY_VERSION: =%

REM Extract version number (remove last .0)
for /f "tokens=1,2,3,4 delims=." %%a in ("%ASSEMBLY_VERSION%") do (
    set VERSION=%%a.%%b.%%c
)

echo Current version: %VERSION%
call :log "Current version: %VERSION%"

REM Ask if increment version
set /p INCREMENT_VERSION="Increment version? (y/n, default n): "
if /i "%INCREMENT_VERSION%"=="y" (
    call :increment_version
)

call :success "Version: %VERSION%"

echo [3/8] Cleaning old files...
call :log "Starting cleanup"

if exist "%TEMP_DIR%" rmdir /s /q "%TEMP_DIR%"
mkdir "%TEMP_DIR%"

if exist "%PROJECT_NAME%.*.nupkg" del "%PROJECT_NAME%.*.nupkg"

call :success "Cleanup completed"

echo [4/8] Building project...
call :log "Starting project build"

msbuild "%PROJECT_FILE%" /p:Configuration=%BUILD_CONFIG% /p:Platform="%PLATFORM%" /t:Clean,Build /v:minimal
if errorlevel 1 (
    call :error "Project build failed"
    goto :end
)

call :success "Build completed"

echo [5/8] Creating NuGet package...
call :log "Starting NuGet package creation"

REM Update version in nuspec file
powershell -Command "(Get-Content '%NUSPEC_FILE%') -replace '<version>.*</version>', '<version>%VERSION%</version>' | Set-Content '%NUSPEC_FILE%'"

nuget pack "%NUSPEC_FILE%"
if errorlevel 1 (
    call :error "NuGet package creation failed"
    goto :end
)

set NUPKG_FILE=%PROJECT_NAME%.%VERSION%.nupkg
if not exist "%NUPKG_FILE%" (
    call :error "NuGet package file %NUPKG_FILE% not found"
    goto :end
)

call :success "NuGet package created: %NUPKG_FILE%"

echo [6/8] Squirrel packaging...
call :log "Starting Squirrel packaging"

Squirrel --releasify "%NUPKG_FILE%" --releaseDir="%TEMP_DIR%"
if errorlevel 1 (
    call :error "Squirrel packaging failed"
    goto :end
)

call :success "Squirrel packaging completed"

echo [7/8] Connecting to NAS server...
call :log "Starting NAS connection"

REM Disconnect existing connection
net use "%NAS_PATH%" /delete >nul 2>&1

REM Establish new connection
net use "%NAS_PATH%" "%NAS_PASS%" /user:"%NAS_USER%"
if errorlevel 1 (
    call :error "NAS server connection failed"
    goto :end
)

call :success "NAS server connected successfully"

echo [8/8] Deploying to NAS...
call :log "Starting deployment to NAS"

REM Backup existing files
if exist "%NAS_PATH%\RELEASES" (
    copy "%NAS_PATH%\RELEASES" "%NAS_PATH%\RELEASES.backup.%date:~0,4%%date:~5,2%%date:~8,2%" >nul
    call :log "Backed up existing RELEASES file"
)

REM Copy new files
xcopy "%TEMP_DIR%\*" "%NAS_PATH%\" /E /Y
if errorlevel 1 (
    call :error "Deployment to NAS failed"
    goto :end
)

call :success "Deployment completed"

echo.
echo ===============================================================
echo                        PUBLISH SUCCESS!
echo.
echo  Version: %VERSION%
echo  Deploy Location: %NAS_PATH%
echo  Log File: %LOG_FILE%
echo ===============================================================
echo.

call :log "Publish process completed"
goto :cleanup

:increment_version
REM Version increment logic
for /f "tokens=1,2,3 delims=." %%a in ("%VERSION%") do (
    set MAJOR=%%a
    set MINOR=%%b
    set PATCH=%%c
)
set /a PATCH+=1
set VERSION=%MAJOR%.%MINOR%.%PATCH%
set NEW_ASSEMBLY_VERSION=%VERSION%.0

REM Update AssemblyInfo.cs
powershell -Command "(Get-Content 'Properties\AssemblyInfo.cs') -replace 'AssemblyVersion\(\".*\"\)', 'AssemblyVersion(\"%NEW_ASSEMBLY_VERSION%\")' -replace 'AssemblyFileVersion\(\".*\"\)', 'AssemblyFileVersion(\"%NEW_ASSEMBLY_VERSION%\")' | Set-Content 'Properties\AssemblyInfo.cs'"

echo Version incremented to: %VERSION%
call :log "Version incremented to: %VERSION%"
goto :eof

:log
echo [%date% %time%] %~1 >> "%LOG_FILE%"
goto :eof

:success
echo [√] %~1
call :log "SUCCESS: %~1"
goto :eof

:error
echo [×] ERROR: %~1
call :log "ERROR: %~1"
goto :eof

:cleanup
REM Clean temporary files
if exist "%TEMP_DIR%" rmdir /s /q "%TEMP_DIR%"
if exist "%NUPKG_FILE%" del "%NUPKG_FILE%"

:end
echo.
echo Press any key to exit...
pause >nul
