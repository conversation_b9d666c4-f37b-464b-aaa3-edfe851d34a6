# RELEASES文件格式指南

## 错误分析："Remote release File is empty or corrupted"

### 常见原因
1. **RELEASES文件为空**：文件存在但没有内容
2. **格式错误**：文件内容不符合Squirrel期望的格式
3. **编码问题**：文件编码不正确（应该是UTF-8）
4. **文件损坏**：传输过程中文件被截断或损坏
5. **权限问题**：文件无法完整读取

## RELEASES文件正确格式

### 基本格式
RELEASES文件是一个纯文本文件，每行包含一个发布包的信息：
```
SHA1哈希值 文件名 文件大小
```

### 示例内容
```
A1B2C3D4E5F6789012345678901234567890ABCD EasyWork.Honor-1.0.0-full.nupkg 15234567
B2C3D4E5F6789012345678901234567890ABCDE EasyWork.Honor-1.0.1-delta.nupkg 2345678
C3D4E5F6789012345678901234567890ABCDEF12 EasyWork.Honor-1.0.1-full.nupkg 15456789
```

### 格式要求
1. **SHA1哈希值**：
   - 必须是40个十六进制字符
   - 大小写不敏感，但通常使用大写
   - 对应.nupkg文件的SHA1校验和

2. **文件名**：
   - 完整的.nupkg文件名
   - 格式：`应用名-版本号-类型.nupkg`
   - 类型：`full`（完整包）或`delta`（增量包）

3. **文件大小**：
   - .nupkg文件的字节数
   - 必须是纯数字

4. **分隔符**：
   - 使用单个空格分隔各部分
   - 不要使用制表符或多个空格

## 检查和修复步骤

### 1. 使用测试工具检查
运行更新的`UpdateTestForm`：
```csharp
var testForm = new UpdateTestForm();
testForm.ShowDialog();
```

点击按钮进行检查：
- **"列出文件"**：查看更新目录中的所有文件
- **"检查RELEASES"**：详细分析RELEASES文件格式
- **"测试连接"**：验证网络连接和文件访问

### 2. 手动验证RELEASES文件

**步骤1：检查文件是否存在**
```cmd
dir "\\172.20.0.20\public\00A-IT信息化\小狗呀\install\RELEASES"
```

**步骤2：查看文件内容**
```cmd
type "\\172.20.0.20\public\00A-IT信息化\小狗呀\install\RELEASES"
```

**步骤3：检查文件大小**
```cmd
dir "\\172.20.0.20\public\00A-IT信息化\小狗呀\install\RELEASES" /Q
```

### 3. 重新生成RELEASES文件

如果RELEASES文件有问题，需要重新生成：

**方法1：重新运行Squirrel打包**
```cmd
Squirrel --releasify EasyWork.Honor.1.0.0.nupkg --releaseDir=输出目录
```

**方法2：手动创建RELEASES文件**
```powershell
# 计算SHA1和文件大小
$file = "EasyWork.Honor-1.0.0-full.nupkg"
$sha1 = (Get-FileHash $file -Algorithm SHA1).Hash
$size = (Get-Item $file).Length
"$sha1 $file $size" | Out-File -FilePath "RELEASES" -Encoding UTF8
```

## 常见问题和解决方案

### 问题1：RELEASES文件为空
**症状**：文件存在但大小为0字节
**解决**：重新运行Squirrel打包命令

### 问题2：格式错误
**症状**：文件有内容但格式不正确
**检查**：
- SHA1是否为40个十六进制字符
- 文件名是否正确
- 文件大小是否为纯数字
- 分隔符是否为单个空格

### 问题3：编码问题
**症状**：文件内容显示乱码
**解决**：确保文件以UTF-8编码保存

### 问题4：文件权限
**症状**：无法读取文件内容
**解决**：检查NAS共享权限和用户权限

## 完整的Squirrel包结构

正确的更新目录应该包含：
```
install/
├── RELEASES                           # 版本信息文件（必需）
├── EasyWork.Honor-1.0.0-full.nupkg   # 完整安装包
├── EasyWork.Honor-1.0.1-delta.nupkg  # 增量更新包（可选）
├── EasyWork.Honor-1.0.1-full.nupkg   # 新版本完整包
├── Setup.exe                          # 安装程序
└── EasyWork.Honor/                    # 应用程序目录
    ├── app-1.0.1/                    # 版本目录
    ├── packages/                      # 包缓存
    └── Update.exe                     # 更新引擎
```

## 调试技巧

### 1. 启用详细日志
在代码中添加：
```csharp
System.Diagnostics.Debug.WriteLine($"RELEASES文件路径: {releasesPath}");
System.Diagnostics.Debug.WriteLine($"文件内容: {content}");
```

### 2. 使用DebugView
下载并运行DebugView工具查看实时调试信息

### 3. 逐步验证
1. 先验证文件存在
2. 再验证文件不为空
3. 然后验证文件格式
4. 最后验证文件内容

通过以上步骤，应该能够识别和解决RELEASES文件的问题。
