@echo off
setlocal enabledelayedexpansion

echo.
echo ===============================================================
echo                    EasyWork.Honor Quick Publish
echo ===============================================================
echo.
echo Select publish method:
echo.
echo [1] Standard Publish (English)
echo [2] Chinese Version Publish
echo [3] PowerShell Advanced Publish
echo [4] PowerShell Auto Increment
echo [0] Exit
echo.

set /p choice="Enter your choice (0-4): "

if "!choice!"=="1" (
    echo Starting standard publish...
    call OneClickPublish.bat
) else if "!choice!"=="2" (
    echo Starting Chinese version publish...
    call "一键发布-中文版.bat"
) else if "!choice!"=="3" (
    echo Starting PowerShell advanced publish...
    powershell -ExecutionPolicy Bypass -File OneClickPublish.ps1
) else if "!choice!"=="4" (
    echo Starting PowerShell auto increment...
    powershell -ExecutionPolicy Bypass -File OneClickPublish.ps1 -IncrementVersion
) else if "!choice!"=="0" (
    echo Exiting...
    exit /b 0
) else (
    echo Invalid choice, please run script again
    pause
    exit /b 1
)

echo.
echo Publish completed!
pause
