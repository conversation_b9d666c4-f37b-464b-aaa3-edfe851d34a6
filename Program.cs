using System;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace EasyWork.Honor
{
    static class Program
    {

        /// <summary>
        /// 应用程序的主入口点。
        /// </summary>
        [STAThread]
        private static void Main()
        {
            Application.EnableVisualStyles();
            Application.SetCompatibleTextRenderingDefault(false);

            // 检查命令行参数，避免重复显示登录窗体
            var args = Environment.GetCommandLineArgs();
            bool isSilentRestart = args.Contains("-silent");

            // 执行自动更新检查
            AutoUpdater.CheckAndUpdateAsync().GetAwaiter().GetResult();

            if (!isSilentRestart)
            {
                Application.Run(new Login());
            }
            else
            {
                Application.Run(new Login()); // 或者直接进入主界面
            }
        }

    }
}
