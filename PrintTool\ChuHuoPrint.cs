﻿using AForge.Video.DirectShow;
using EasyWork.Honor.Service;
using EasyWork.Honor.Service.Print;
using EasyWork.Model;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.IO;
using System.IO.Ports;
using System.Linq;
using System.Net;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace EasyWork.Honor.PrintTool
{
    public partial class ChuHuoPrint : Form
    {
        string rl = "^LS0";
        string ud = "^LT0";
        SerialPort myComPort;
        string ScriptText;
        bool shop = false,photoLock;
        string text, VeneerCode,ShowSN;
        int photosqty = 0;
        ZPLPrint zb;
        DataTable dt, wdt;
        bool weshop = false;
        private FilterInfoCollection videoDevices;
        private VideoCaptureDevice videoDevice;
        private VideoCapabilities[] videoCapabilities;
        private DataTable celt = new DataTable();
        public ChuHuoPrint()
        {
            InitializeComponent();
            CheckForIllegalCrossThreadCalls = false;
            string[] str = SerialPort.GetPortNames();
            if (str != null)
            {
                //添加串口项目  
                foreach (string s in SerialPort.GetPortNames())
                {//获取有多少个COM口  
                    cb_weight_port.Items.Add(s);
                }
                if (cb_weight_port.Items.Count > 0)
                {
                    cb_weight_port.SelectedIndex = 0;
                }
            }


            ScriptText = PrintService.Get_ZPLScript("维修主板盒标", "ALL");

            videoDevices = new FilterInfoCollection(FilterCategory.VideoInputDevice);
            if (videoDevices.Count != 0)
            {
                foreach (FilterInfo device in videoDevices)
                {
                    cmbCamera.Items.Add(device.Name);
                }

                cmbCamera.SelectedIndex = 0;
            }
            else
            {
                cmbCamera.Items.Add("没有找到摄像头");
            }
            try
            {
                cmbResolution.Text = "1024 x 768";
            }
            catch
            {

            }


        }

        private void bt_Print_Click(object sender, EventArgs e)
        {
            lb_show.Text = "";
            

            //临时
            if (bt_weight_openport.Enabled)
            {
                lb_show.Text = "请先打开称的端口";
                return;
            }
            if (btnConnect.Enabled)
            {
                lb_show.Text = "请先打开摄像头的端口";
                return;
            }
            if (tb_PhoneCode.Enabled)
            {
                if (tb_PhoneCode.Text.Trim()=="")
                {
                    tb_PhoneCode.Focus();
                    return;
                }
                if (tb_PhoneCode.Text.Trim().Length != 8 && tb_PhoneCode.Text.Trim().Length != 12)
                {
                    lb_show.Text = "编码必须为8位或12位";
                    tb_PhoneCode.SelectAll();
                    return;
                }
                if (cb_PhoneCode.Checked)
                {
                    tb_PhoneCode.Enabled = false;
                }
            }

            if (tb_MEID.Enabled)
            {
                if (tb_MEID.Text.Trim() == "")
                {
                    tb_MEID.Focus();
                    return;
                }
                if (!new[] { 12, 14, 15, 16 }.Contains(tb_MEID.Text.Trim().Length))
                {
                    lb_show.Text = "MEID必须为12/14/15/16位字符";
                    tb_MEID.SelectAll();
                    return;
                }

                string sql = "select psid,targetcode,Old_IMEI,New_IMEI,VeneerCode,sn,Department,psid,PhoneModel,InitialCode,outtype,type,location from MES_Honor.dbo.WXMES where new_imei='" + tb_MEID.Text.Trim() + "'";
                dt = SqlHelper.Query(sql).Tables[0];

                string sqlstr = "select * from MES_Honor.dbo.CELT_Data where PhysicsNo='" + tb_MEID.Text.Trim() + "'";
                celt = SqlHelper.Query(sqlstr).Tables[0];

                if (dt.Rows.Count == 0)
                {
                    lb_show.Text = "系统没有该MEID数据";
                    insertChuHuo(tb_PhoneCode.Text.Trim(), tb_VeneerCode.Text.Trim(), tb_MEID.Text.Trim(), "0", lb_show.Text, tb_Weight.Text.Trim(), "", "", "", "", "", "", "", "", "", "");
                    return;
                }

                if (dt.Rows[0]["location"].ToString() != "Packing" && dt.Rows[0]["location"].ToString() != "Complete")
                {
                    lb_show.Text = $"该MEID号在{dt.Rows[0]["location"]?.ToString()},不在可打标状态";
                    insertChuHuo(tb_PhoneCode.Text.Trim(), tb_VeneerCode.Text.Trim(), tb_MEID.Text.Trim(), "0", lb_show.Text, tb_Weight.Text.Trim(), "", "", "", "", "", "", "", "", "", "");
                    return;
                }
                VeneerCode = dt.Rows[0]["VeneerCode"].ToString();

                ShowSN = celt.Rows[0]["Product_Barcode"].ToString();

                tb_MEID.Enabled = false;

                if (dt.Rows[0]["Department"].ToString() == "PC主板")
                {
                    tbNewSN.Text = ShowSN;
                    tbNewSN.Enabled = false;
                    tbPhotos.Enabled = true;
                    tbPhotos.Focus();
                    tbPhotos.SelectAll();
                    photosqty = 0;
                    tbPhotos.Text = "请拍照,正反面各一张";
                    return;
                }
                tbNewSN.Text = ShowSN;

                photoLock = !string.IsNullOrEmpty(tbNewSN.Text);
            }

            if (photoLock)
            {
                photoLock = false;
                tbPhotos.Enabled = true;
                tbPhotos.Focus();
                tbPhotos.SelectAll();
                photosqty = 0;
                tbPhotos.Text = "请拍照,正反面各一张";
                return;
            }

            if (tbPhotos.Enabled)
            {
                //取消网标校验了
                //string[] model = dt.Rows[0]["PhoneModel"].ToString().ToLower().Split('-');
                //if (new[] { "karajan2", "britten", "karajanr", "ags3m", "nottingham", "hendry", "ali", "konanz", "cartier", "andygt", "rhea", "annagt", "dior", "piagets", "piaget", "rocky", "rheap", "gaudi", "woody" }.Contains(model[0]))
                //{
                //    tbPhotos.Enabled = false;
                //    tb_VeneerCode.Enabled = true;
                //    tb_VeneerCode.Focus();
                //    tb_VeneerCode.SelectAll();
                //    return;
                //}
                //if (dt.Rows[0]["Department"].ToString()=="维修部门"&& dt.Rows[0]["PhoneModel"].ToString().Split('-')[0].ToLower()!= "nottingham")
                //{
                //    tbPhotos.Enabled = false;
                //    tbNetCode.Enabled = true;
                //    tbNetCode.Focus();
                //    tbNetCode.SelectAll();
                //}
                //else if(dt.Rows[0]["Department"].ToString() == "PC主板"|| dt.Rows[0]["PhoneModel"].ToString().Split('-')[0].ToLower() == "nottingham")
                //{
                //    tbPhotos.Enabled = false;
                //    tb_VeneerCode.Enabled = true;
                //    tb_VeneerCode.Focus();
                //    tb_VeneerCode.SelectAll();

                //}

                tbPhotos.Enabled = false;
                tb_VeneerCode.Enabled = true;
                tb_VeneerCode.Focus();
                tb_VeneerCode.SelectAll();
                return;
            }


            //if (tbNetCode.Enabled)
            //{
            //    lb_show.Text = "";
            //    if (tbNetCode.Text.Trim().Length!=15)
            //    {
            //        tbNetCode.SelectAll();
            //        return;
            //    }
            //    tbNetCode.Enabled = false;
            //    tb_VeneerCode.Enabled = true;
            //}

            if (tb_VeneerCode.Enabled)
            {
                if (tb_VeneerCode.Text.Trim() == "")
                {
                    tb_VeneerCode.Focus();
                    return;
                }
                if (!new[] { 15, 16 }.Contains(tb_VeneerCode.Text.Trim().Length))
                {
                    lb_show.Text = "单板号/IMEI1必须为15/16位字符";
                    tb_VeneerCode.SelectAll();
                    return;
                }
                tb_VeneerCode.Enabled = false;




                string type = dt.Rows[0]["targetcode"].ToString().Substring(0, 2) == "03"
        ? "主板"
        : "主机";
                string wsql = "select *  from MES_Honor.dbo.codingmanage where code='" + tb_PhoneCode.Text.Trim() + "' and codetype='" + type + "'";
                wdt = SqlHelper.Query(wsql).Tables[0];
            }





            if (dt.Rows[0]["Department"].ToString() != "PC主板")
            {

                if (wdt.Rows.Count == 0)
                {
                    lb_show.Text = "系统没有该编码的重量数据";
                    insertChuHuo(tb_PhoneCode.Text.Trim(), tb_VeneerCode.Text.Trim(), tb_MEID.Text.Trim(), "0", lb_show.Text, tb_Weight.Text.Trim(), dt.Rows[0]["Department"].ToString(), dt.Rows[0]["psid"].ToString(), dt.Rows[0]["phonemodel"].ToString(), dt.Rows[0]["InitialCode"].ToString(), dt.Rows[0]["sn"].ToString(), dt.Rows[0]["old_imei"].ToString(), dt.Rows[0]["outtype"].ToString(), dt.Rows[0]["type"].ToString(), "", tbNetCode.Text.Trim());
                    return;
                }

                if (tb_PhoneCode.Text.Trim().ToUpper() != dt.Rows[0]["targetcode"].ToString().ToUpper())
                {
                    lb_show.Text = "编码与系统不匹配";
                    insertChuHuo(tb_PhoneCode.Text.Trim(), tb_VeneerCode.Text.Trim(), tb_MEID.Text.Trim(), "0", lb_show.Text, tb_Weight.Text.Trim(), dt.Rows[0]["Department"].ToString(), dt.Rows[0]["psid"].ToString(), dt.Rows[0]["phonemodel"].ToString(), dt.Rows[0]["InitialCode"].ToString(), dt.Rows[0]["sn"].ToString(), dt.Rows[0]["old_imei"].ToString(), dt.Rows[0]["outtype"].ToString(), dt.Rows[0]["type"].ToString(), wdt.Rows[0]["weight"].ToString(), tbNetCode.Text.Trim());
                    return;
                }

                if (tb_VeneerCode.Text.Trim().ToUpper() != dt.Rows[0]["veneercode"].ToString().ToUpper() && dt.Rows[0]["targetcode"].ToString().Trim().Substring(0, 2) == "03")
                {
                    lb_show.Text = "单板号与系统不匹配";
                    insertChuHuo(tb_PhoneCode.Text.Trim(), tb_VeneerCode.Text.Trim(), tb_MEID.Text.Trim(), "0", lb_show.Text, tb_Weight.Text.Trim(), dt.Rows[0]["Department"].ToString(), dt.Rows[0]["psid"].ToString(), dt.Rows[0]["phonemodel"].ToString(), dt.Rows[0]["InitialCode"].ToString(), dt.Rows[0]["sn"].ToString(), dt.Rows[0]["old_imei"].ToString(), dt.Rows[0]["outtype"].ToString(), dt.Rows[0]["type"].ToString(), wdt.Rows[0]["weight"].ToString(), tbNetCode.Text.Trim());
                    return;
                }

                if (dt.Rows[0]["targetcode"].ToString().Trim().Substring(0, 2) != "03" && dt.Rows[0]["targetcode"].ToString().Trim().Substring(0, 2) != "51")
                {
                    lb_show.Text = "编码有误";
                    return;
                }
            }


            string netcode = "", SqlReprint = string.Format(" select * from mes_honor.dbo.wxmes_log  where new_imei='{0}' and veneercode='{1}' and approve ='1' and OriginalFault like '标1' and Location = 'ChuHuo'", tb_MEID.Text.Trim(), tb_VeneerCode.Text.Trim()), flag = "";

            if (!shop)
            {
                string sn = dt.Rows[0]["SN"].ToString();
                if (dt.Rows[0]["Department"].ToString() == "维修部门")
                {

                    if (celt.Rows.Count == 0)
                    {

                        lb_show.Text = "该单板没有CELT的数据文件";
                        insertChuHuo(tb_PhoneCode.Text.Trim(), tb_VeneerCode.Text.Trim(), tb_MEID.Text.Trim(), "0", lb_show.Text, tb_Weight.Text.Trim(), dt.Rows[0]["Department"].ToString(), dt.Rows[0]["psid"].ToString(), dt.Rows[0]["phonemodel"].ToString(), dt.Rows[0]["InitialCode"].ToString(), dt.Rows[0]["sn"].ToString(), dt.Rows[0]["old_imei"].ToString(), dt.Rows[0]["outtype"].ToString(), dt.Rows[0]["type"].ToString(), wdt.Rows[0]["weight"].ToString(), celt.Rows[0]["NetCode"].ToString().ToUpper());
                        return;
                    }
                    if (celt.Rows[0]["Product_Barcode"].ToString() != tbNewSN.Text.Trim())
                    {
                        lb_show.Text = "扫描的新SN号与CELT的数据文件的新SN号不符";
                        insertChuHuo(tb_PhoneCode.Text.Trim(), tb_VeneerCode.Text.Trim(), tb_MEID.Text.Trim(), "0", lb_show.Text, tb_Weight.Text.Trim(), dt.Rows[0]["Department"].ToString(), dt.Rows[0]["psid"].ToString(), dt.Rows[0]["phonemodel"].ToString(), dt.Rows[0]["InitialCode"].ToString(), dt.Rows[0]["sn"].ToString(), dt.Rows[0]["old_imei"].ToString(), dt.Rows[0]["outtype"].ToString(), dt.Rows[0]["type"].ToString(), wdt.Rows[0]["weight"].ToString(), celt.Rows[0]["NetCode"].ToString().ToUpper());
                        return;
                    }

                    if (tb_VeneerCode.Text.Trim().ToUpper() != celt.Rows[0]["imei1"].ToString().ToUpper() && dt.Rows[0]["targetcode"].ToString().Trim().Substring(0, 2) == "51")
                    {
                        lb_show.Text = "IMEI1与系统不匹配";
                        insertChuHuo(tb_PhoneCode.Text.Trim(), tb_VeneerCode.Text.Trim(), tb_MEID.Text.Trim(), "0", lb_show.Text, tb_Weight.Text.Trim(), dt.Rows[0]["Department"].ToString(), dt.Rows[0]["psid"].ToString(), dt.Rows[0]["phonemodel"].ToString(), dt.Rows[0]["InitialCode"].ToString(), dt.Rows[0]["sn"].ToString(), dt.Rows[0]["old_imei"].ToString(), dt.Rows[0]["outtype"].ToString(), dt.Rows[0]["type"].ToString(), wdt.Rows[0]["weight"].ToString(), celt.Rows[0]["NetCode"].ToString().ToUpper());
                        return;
                    }
                    //string[] model = dt.Rows[0]["PhoneModel"].ToString().ToLower().Split('-');
                    //if (tbNetCode.Text.Trim().ToUpper() != celt.Rows[0]["NetCode"].ToString().ToUpper() && !new[] { "karajan2", "britten", "karajanr", "ags3m", "nottingham", "hendry", "ali", "konanz", "cartier", "andygt", "rhea", "annagt", "dior", "piagets", "piaget", "rocky", "rheap", "gaudi", "woody" }.Contains(model[0]))
                    //{
                    //    lb_show.Text = "网标不符";
                    //    insertChuHuo(tb_PhoneCode.Text.Trim(), tb_VeneerCode.Text.Trim(), tb_MEID.Text.Trim(), "0", lb_show.Text, tb_Weight.Text.Trim(), dt.Rows[0]["Department"].ToString(), dt.Rows[0]["psid"].ToString(), dt.Rows[0]["phonemodel"].ToString(), dt.Rows[0]["InitialCode"].ToString(), dt.Rows[0]["sn"].ToString(), dt.Rows[0]["old_imei"].ToString(), dt.Rows[0]["outtype"].ToString(), dt.Rows[0]["type"].ToString(), wdt.Rows[0]["weight"].ToString(), celt.Rows[0]["NetCode"].ToString().ToUpper());
                    //    return;
                    //}
                    sn = celt.Rows[0]["Product_Barcode"].ToString().ToUpper().Trim();
                    netcode = celt.Rows[0]["NetCode"].ToString().ToUpper();
                }


                text = ScriptText;
                for (int j = 0; j < 5; j++)
                {
                    text = dt.Rows[0]["Department"].ToString() == "维修部门" ? text.Replace("$MEID_HEX_14$", sn) : text.Replace("$MEID_HEX_14$", tbNewSN.Text.Trim());
                    text = text.Replace("$Item_BOM$", dt.Rows[0]["targetcode"].ToString().ToUpper().Trim());
                    if (dt.Rows[0]["targetcode"].ToString().Trim().Substring(0, 2) == "51")
                    {
                        text = text.Replace("$PhoneModel$", wdt.Rows[0]["PhoneModel"].ToString().ToUpper().Trim());
                    }
                }


                //if (SqlHelper.Query(SqlReprint + "and OriginalFault = '标1'").Tables[0].Rows.Count > 0)
                //{
                //    PrintVerify window = new PrintVerify(this);
                //    DialogResult result = window.ShowDialog();
                //    if (result == DialogResult.OK && window.IsPasswordCorrect)
                //    {
                //        flag = "[密码打印]";
                //    }
                //    else
                //    {
                //        lb_show.Text = "打印密码错误！";
                //        insertChuHuo(tb_PhoneCode.Text.Trim(), tb_VeneerCode.Text.Trim(), tb_MEID.Text.Trim(), "0", "[重复打印]"+lb_show.Text, tb_Weight.Text.Trim(), dt.Rows[0]["Department"].ToString(), dt.Rows[0]["psid"].ToString(), dt.Rows[0]["phonemodel"].ToString(), dt.Rows[0]["InitialCode"].ToString(), dt.Rows[0]["sn"].ToString(), dt.Rows[0]["old_imei"].ToString(), dt.Rows[0]["outtype"].ToString(), dt.Rows[0]["type"].ToString(), wdt.Rows[0]["weight"].ToString(), sn);
                //        return;
                //    }
                //}

                if (SqlHelper.Query(SqlReprint).Tables[0].Rows.Count > 0)
                {
                    PrintVerify window = new PrintVerify(this);
                    DialogResult result = window.ShowDialog();
                    if (result == DialogResult.OK && window.IsPasswordCorrect)
                    {
                        flag = "[密码打印]";
                    }
                    else
                    {
                        lb_show.Text = "打印密码错误！";
                        insertChuHuo(tb_PhoneCode.Text.Trim(), tb_VeneerCode.Text.Trim(), tb_MEID.Text.Trim(), "0", lb_show.Text, tb_Weight.Text.Trim(), dt.Rows[0]["Department"].ToString(), dt.Rows[0]["psid"].ToString(), dt.Rows[0]["phonemodel"].ToString(), dt.Rows[0]["InitialCode"].ToString(), dt.Rows[0]["sn"].ToString(), dt.Rows[0]["old_imei"].ToString(), dt.Rows[0]["outtype"].ToString(), dt.Rows[0]["type"].ToString(), wdt.Rows[0]["weight"].ToString(), netcode);
                        return;
                    }
                }


                if (dt.Rows[0]["Department"].ToString() != "PC主板")
                {
                    insertChuHuo(tb_PhoneCode.Text.Trim(), tb_VeneerCode.Text.Trim(), tb_MEID.Text.Trim(), "1", flag+"标1", tb_Weight.Text.Trim(), dt.Rows[0]["Department"].ToString(), dt.Rows[0]["psid"].ToString(), dt.Rows[0]["phonemodel"].ToString(), dt.Rows[0]["InitialCode"].ToString(), dt.Rows[0]["sn"].ToString(), dt.Rows[0]["old_imei"].ToString(), dt.Rows[0]["outtype"].ToString(), dt.Rows[0]["type"].ToString(), wdt.Rows[0]["weight"].ToString(), sn);
                }
                else
                {
                    insertChuHuo(tb_PhoneCode.Text.Trim(), tb_VeneerCode.Text.Trim(), tb_MEID.Text.Trim(), "1", flag+"标1", "", dt.Rows[0]["Department"].ToString(), dt.Rows[0]["psid"].ToString(), dt.Rows[0]["phonemodel"].ToString(), dt.Rows[0]["InitialCode"].ToString(), dt.Rows[0]["sn"].ToString(), dt.Rows[0]["old_imei"].ToString(), dt.Rows[0]["outtype"].ToString(), dt.Rows[0]["type"].ToString(), "", tbNewSN.Text.Trim());
                }

                zb = new ZPLPrint();

                zb.ZPL_Print(text);


                shop = true;

                if (dt.Rows[0]["Department"].ToString().Contains("PC"))
                {
                    bt_clear_Click(null, null);
                    return;
                }


                tb_VeneerCode.Enabled = false;
                tb_Weight.Enabled = true;
            }
            //临时取消
            if (tb_Weight.Text.Trim() == "")
            {
                tb_Weight.Focus();
                return;
            }

            if (wdt.Rows[0]["error"].ToString() == "")
            {
                wdt.Rows[0]["error"] = "0";
            }

            //临时取消
            if (decimal.Parse(tb_Weight.Text.Trim()) > decimal.Parse(wdt.Rows[0]["weight"].ToString()) + decimal.Parse(wdt.Rows[0]["error"].ToString()) || decimal.Parse(tb_Weight.Text.Trim()) < decimal.Parse(wdt.Rows[0]["weight"].ToString()) - decimal.Parse(wdt.Rows[0]["error"].ToString()))
            {
                lb_show.Text = string.Format("你的重量为{0},标准重量为{1},误差±{2}", tb_Weight.Text.Trim(), wdt.Rows[0]["weight"].ToString(), wdt.Rows[0]["error"].ToString()); ;
                insertChuHuo(tb_PhoneCode.Text.Trim(), tb_VeneerCode.Text.Trim(), tb_MEID.Text.Trim(), "0", lb_show.Text, tb_Weight.Text.Trim(), dt.Rows[0]["Department"].ToString(), dt.Rows[0]["psid"].ToString(), dt.Rows[0]["phonemodel"].ToString(), dt.Rows[0]["InitialCode"].ToString(), dt.Rows[0]["sn"].ToString(), dt.Rows[0]["old_imei"].ToString(), dt.Rows[0]["outtype"].ToString(), dt.Rows[0]["type"].ToString(), wdt.Rows[0]["weight"].ToString(), netcode);
                return;
            }
            lb_show.Text = "验证成功";

            if (string.IsNullOrEmpty(flag))
            {
                flag = "Complete";
            }
            insertChuHuo(tb_PhoneCode.Text.Trim(), tb_VeneerCode.Text.Trim(), tb_MEID.Text.Trim(), "1", flag+"标2", tb_Weight.Text.Trim(), dt.Rows[0]["Department"].ToString(), dt.Rows[0]["psid"].ToString(), dt.Rows[0]["phonemodel"].ToString(), dt.Rows[0]["InitialCode"].ToString(), dt.Rows[0]["sn"].ToString(), dt.Rows[0]["old_imei"].ToString(), dt.Rows[0]["outtype"].ToString(), dt.Rows[0]["type"].ToString(), wdt.Rows[0]["weight"].ToString(), netcode);

            updatemes(tb_MEID.Text.Trim(), tb_VeneerCode.Text.Trim());
            zb = new ZPLPrint();
            zb.ZPL_Print(text);
            bt_clear_Click(null, null);

        }

        public void updatemes(string newimei, string veneercode)
        {
            string sql = "update mes_honor.dbo.wxmes set state='Complete',location='Complete' where new_imei='" + newimei + "' and veneercode='" + veneercode + "'";
            SqlHelper.ExecuteSql(sql);
        }

        public void insertChuHuo(string targetcode, string veneercode, string new_imei, string approve, string OriginalFault, string remarks, string Department, string psid, string PhoneModel, string InitialCode, string sn, string old_imei, string outtype, string type, string weight, string netcode)
        {
            weight = weight + "," + netcode;
            string sql = "insert into mes_honor.dbo.wxmes_log(targetcode,veneercode,new_imei,approve,location,OriginalFault,remarks,operatetype,createbyid,createtime,Department,psid,PhoneModel,InitialCode,sn,old_imei,outtype,type,other) values('" + targetcode + "','" + veneercode + "','" + new_imei + "','" + approve + "','ChuHuo','" + OriginalFault + "','" + remarks + "','Job','" + Login.usercode + "',getdate(),'" + Department + "','" + psid + "','" + PhoneModel + "','" + InitialCode + "','" + sn + "','" + old_imei + "','" + outtype + "','" + type + "','" + weight + "')";
            SqlHelper.ExecuteSql(sql);
        }

        private void tb_PhoneCode_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode==Keys.Enter)
            {
                if (tb_PhoneCode.Text.Trim().Substring(0, 2)=="03")
                {
                    ScriptText = PrintService.Get_ZPLScript("维修主板盒标", "ALL");
                    ScriptText = ScriptText.Replace("^LS0", rl);
                    ScriptText = ScriptText.Replace("^LT0", ud);
                    label2.Text = "单板号:";
                }
                else if (tb_PhoneCode.Text.Trim().Substring(0, 2) == "51")
                {
                    ScriptText = PrintService.Get_ZPLScript("翻新主机盒标", "ALL");
                    ScriptText = ScriptText.Replace("^LS0", rl);
                    ScriptText = ScriptText.Replace("^LT0", ud);
                    label2.Text = "IMEI1:";
                }
                bt_Print_Click(null, null);
            }
        }

        private void tb_MEID_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                bt_Print_Click(null, null);
            }
        }

        private void NewSN_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                bt_Print_Click(null, null);
            }
        }

        private void tb_VeneerCode_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                bt_Print_Click(null, null);
            }
        }

        private void tb_Weight_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                bt_Print_Click(null, null);
            }
        }

        private void tb_Weight_KeyPress(object sender, KeyPressEventArgs e)
        {
            string s = "1234567890." + (char)8;
            if (s.IndexOf(e.KeyChar.ToString()) < 0)
            {
                e.Handled = true;
            }
        }



        private void bt_weight_openport_Click(object sender, EventArgs e)
        {
            try
            {
                myComPort = new SerialPort(cb_weight_port.Text, 9600, Parity.None);
                myComPort.DataReceived += ReceiveData;
                myComPort.Open();
                bt_weight_openport.Text = "端口已打开";
                bt_weight_openport.Enabled = false;
            }
            catch
            {

            }
        }

        private void ReceiveData(object sender, SerialDataReceivedEventArgs e)
        {
            int n = myComPort.BytesToRead;
            byte[] buf = new byte[n];
            myComPort.Read(buf, 0, n);
            if (tb_Weight.Enabled)
            {
                tb_Weight.Invoke(new EventHandler(delegate {
                    if (tb_Weight.Enabled)
                    {
                        tb_Weight.Text += Regex.Replace(Encoding.ASCII.GetString(buf), @"[^\d.\d]", "");
                    }
                }));
            }

            if (tb_Weight.Text.Trim().Length>=5)
            {
                bt_Print_Click(null, null);
            }
        }

        private void bt_clear_Click(object sender, EventArgs e)
        {
            tb_VeneerCode.Text = "";
            tb_MEID.Text = "";
            tb_Weight.Text = "";
            tbNetCode.Text = "";
            VeneerCode = "";
            photosqty = 0;
            tbNewSN.Text = "";
            tbPhotos.Text = "";
            tb_MEID.Enabled = true;
            tb_VeneerCode.Enabled =false;
            tbNetCode.Enabled = false;
            tb_Weight.Enabled = false;
            shop =weshop= false;
            if (!cb_PhoneCode.Checked)
            {
                tb_PhoneCode.Text = "";
                tb_PhoneCode.Enabled = true;
                tb_PhoneCode.Focus();
                return;
            }
            tb_MEID.Focus();
        }

        private void cb_PhoneCode_CheckedChanged(object sender, EventArgs e)
        {
            if (!cb_PhoneCode.Checked)
            {
                tb_PhoneCode.Enabled = true;
            }
        }


        private void textBox1_Validating(object sender, CancelEventArgs e)
        {
            ScriptText = ScriptText.Replace(rl, "^LS" + textBox1.Text.Trim());
            rl = "^LS" + textBox1.Text.Trim();
        }

        private void textBox1_KeyPress(object sender, KeyPressEventArgs e)
        {
            string s = "-1234567890" + (char)8;
            if (s.IndexOf(e.KeyChar.ToString()) < 0)
            {
                e.Handled = true;
            }
        }

        private void textBox2_Validating(object sender, CancelEventArgs e)
        {
            ScriptText = ScriptText.Replace(ud, "^LT" + textBox2.Text.Trim());
            rl = "^LT" + textBox1.Text.Trim();
        }

        private void tbNetCode_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                bt_Print_Click(null, null);
            }
        }

        private void btnConnect_Click(object sender, EventArgs e)
        {
            if (videoDevice != null)
            {
                if ((videoCapabilities != null) && (videoCapabilities.Length != 0))
                {
                    videoDevice.VideoResolution = videoCapabilities[cmbResolution.SelectedIndex];

                    vispShoot.VideoSource = videoDevice;
                    vispShoot.Start();
                    EnableControlStatus(false);
                }
            }
        }

        private void cmbCamera_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (videoDevices.Count != 0)
            {
                videoDevice = new VideoCaptureDevice(videoDevices[cmbCamera.SelectedIndex].MonikerString);
                GetDeviceResolution(videoDevice);
            }
        }

        private void GetDeviceResolution(VideoCaptureDevice videoCaptureDevice)
        {
            cmbResolution.Items.Clear();
            videoCapabilities = videoCaptureDevice.VideoCapabilities;
            foreach (VideoCapabilities capabilty in videoCapabilities)
            {
                cmbResolution.Items.Add($"{capabilty.FrameSize.Width} x {capabilty.FrameSize.Height}");
            }
            //cmbResolution.SelectedIndex = 0;
        }

        private void EnableControlStatus(bool status)
        {
            cmbCamera.Enabled = status;
            cmbResolution.Enabled = status;
            btnConnect.Enabled = status;
            button2.Enabled = !status;
            btnDisconnect.Enabled = !status;
        }

        private void btnDisconnect_Click(object sender, EventArgs e)
        {
            DisConnect();
            EnableControlStatus(true);
        }

        private void DisConnect()
        {
            if (vispShoot.VideoSource != null)
            {
                vispShoot.SignalToStop();
                vispShoot.WaitForStop();
                vispShoot.VideoSource = null;
            }
        }

        private void button2_Click(object sender, EventArgs e)
        {
            if (!tbPhotos.Enabled)
            {
                tbPhotos.Text = photosqty >= 2 ? $"已完成{photosqty}张照片" : "还没有到拍照的时候";
                return;
            }
            string str = UploadPhone();
            if (str != "1")
            {
                MessageBox.Show(str);
                return;
            }

            photosqty++;
            if (photosqty == 1)
            {
                tbPhotos.Text = "已完成1张照片";
                return;
            }
            if (photosqty >= 2)
            {
                tbPhotos.Text = "已完成2张照片";
                bt_Print_Click(null, null);
            }

        }

        private void tbPhotos_KeyDown(object sender, KeyEventArgs e)
        {
            //if (tbPhotos.Text.Length>=4)
            //{
            //    string str = UploadPhone();
            //    if (str != "1")
            //    {
            //        MessageBox.Show(str);
            //        tbPhotos.Text = str;
            //        return;
            //    }
            //    else
            //    {
            //        photosqty++;
            //        tbPhotos.Text = $"已拍{photosqty}张照片";
            //    }

            //    if (photosqty>=2)
            //    {
            //        bt_Print_Click(null,null);
            //    }
            //}
        }

        private void groupBox2_Enter(object sender, EventArgs e)
        {

        }

        public string UploadPhone()
        {
            Bitmap img = vispShoot.GetCurrentVideoFrame();
            if (img == null)
            {
                return "请选打开摄像头";
            }
            //添加水印
            var myGraphic = Graphics.FromImage(img);
            var sourceString = $"出货-【{VeneerCode}】-{DateTime.Now:g}";
            var font = new Font("Arial", 10);
            var size = myGraphic.MeasureString(sourceString, font);
            myGraphic.DrawString(sourceString, font, System.Drawing.Brushes.Red, new PointF(img.Width - size.Width - 15, img.Height - size.Height - 15));
            myGraphic.Dispose();

            if (VeneerCode=="")
            {
                return "请先查询数据";
            }

            string fileurl = @"D:\MES\主板出货\";
            if (!File.Exists(fileurl))
            {
                DirectoryInfo di = new DirectoryInfo(fileurl);
                di.Create();
            }

            //Login.usercode = "admin";
            string filename = VeneerCode + "_" + Login.usercode + "_" + PublicService.serverTime().ToString("yyyyMMddHHmmss") + ".jpg";
            img.Save(fileurl + filename);
            string url = "http://***********:21321/api/ChuHuoUpload";
            WebClient web = new WebClient();
            web.Headers.Add("Content-Type", filename);
            byte[] a = web.UploadFile(url, "POST", fileurl + filename);
            string str = Encoding.UTF8.GetString(a);



            return str;

        }

        private void cb_MD_print_SelectedIndexChanged(object sender, EventArgs e)
        {
            ScriptText = ScriptText.Replace("^MD10", "^MD" + cb_MD_print.Text.Trim());
        }
    }
}
