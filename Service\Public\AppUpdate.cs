using System;
using System.Configuration;
using System.Linq;
using System.Net;
using System.Runtime.InteropServices;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Forms;
using Squirrel;

namespace EasyWork.Honor.Service
{
    ///<summary>
    ///自动更新管理器
    ///</summary>
    public static class AppUpdate
    {
        #region Windows API for Network Authentication
        [DllImport("mpr.dll")]
        private static extern int WNetAddConnection2(NetResource netResource, string password, string username, int flags);

        [DllImport("mpr.dll")]
        private static extern int WNetCancelConnection2(string name, int flags, bool force);

        [StructLayout(LayoutKind.Sequential)]
        public class NetResource
        {
            public int Scope = 0;
            public int Type = 0;
            public int DisplayType = 0;
            public int Usage = 0;
            public string LocalName = "";
            public string RemoteName = "";
            public string Comment = "";
            public string Provider = "";
        }
        #endregion


        #region this app update methods and properties  

        ///<summary>
        ///检查并执行自动更新
        ///</summary>
        ///<returns></returns>
        public static async Task CheckAndUpdateAsync()
        {
            ProgressForm progressForm = null;
            CancellationTokenSource cancellationTokenSource = null;

            try
            {
                var updateURL = ConfigurationManager.AppSettings["UpdateURL"];
                if (string.IsNullOrEmpty(updateURL))
                {
                    System.Diagnostics.Debug.WriteLine("UpdateURL 未配置");
                    return;
                }

                //设置网络超时（针对NAS远程访问）
                ServicePointManager.DefaultConnectionLimit = 10;
                ServicePointManager.Expect100Continue = false;

                //处理 NAS 身份验证
                await HandleNasAuthentication(updateURL);

                using (var mgr = new UpdateManager(updateURL, "EasyWork.Honor"))
                {
                    System.Diagnostics.Debug.WriteLine($"检查更新: {updateURL}");
                    var updateInfo = await mgr.CheckForUpdate();

                    if (!updateInfo.ReleasesToApply.Any())
                    {
                        System.Diagnostics.Debug.WriteLine("没有可用的更新");
                        return;
                    }

                    var result = MessageBox.Show("发现新版本，是否立即更新？", "更新提示",
                        MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                    if (result != DialogResult.Yes)
                        return;

                    //创建进度窗体和取消令牌
                    cancellationTokenSource = new CancellationTokenSource();
                    progressForm = new ProgressForm("正在下载更新...")
                    {
                        TopMost = true,
                        ShowInTaskbar = false
                    };
                    progressForm.Show();
                    progressForm.BringToFront();

                    //进度更新委托
                    var progress = new Progress<int>(percent =>
                    {
                        UpdateProgressSafely(progressForm, percent);
                    });

                    //启动模拟进度任务
                    var fakeProgressTask = StartFakeProgressTask(progress, cancellationTokenSource.Token);

                    System.Diagnostics.Debug.WriteLine("开始下载更新...");

                    //下载更新包
                    await mgr.DownloadReleases(
                        updateInfo.ReleasesToApply,
                        percent =>
                        {
                            //真实进度开始时取消模拟进度
                            cancellationTokenSource?.Cancel();
                            System.Diagnostics.Debug.WriteLine($"下载进度: {percent}%");
                            UpdateProgressSafely(progressForm, percent);
                        });

                    //确保显示100%完成
                    UpdateProgressSafely(progressForm, 100);
                    await Task.Delay(500); //让用户看到100%完成状态

                    System.Diagnostics.Debug.WriteLine("下载完成，开始应用更新...");

                    //关闭进度窗体
                    CloseProgressFormSafely(progressForm);
                    progressForm = null;

                    //应用更新
                    await mgr.ApplyReleases(updateInfo);

                    MessageBox.Show("更新完成，程序将自动重启。", "提示",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);

                    UpdateManager.RestartApp("-silent");
                }
            }
            catch (WebException webEx)
            {
                System.Diagnostics.Debug.WriteLine($"网络异常: {webEx.Message}");
                ShowErrorMessage($"网络连接失败，请检查网络或NAS连接状态：{webEx.Message}");
            }
            catch (UnauthorizedAccessException authEx)
            {
                System.Diagnostics.Debug.WriteLine($"权限异常: {authEx.Message}");
                ShowErrorMessage($"访问权限不足，请检查NAS共享权限：{authEx.Message}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"更新异常: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"异常堆栈: {ex.StackTrace}");
                ShowErrorMessage($"自动更新失败：{ex.Message}");
            }
            finally
            {
                //清理资源
                cancellationTokenSource?.Cancel();
                cancellationTokenSource?.Dispose();

                if (progressForm != null && !progressForm.IsDisposed)
                {
                    CloseProgressFormSafely(progressForm);
                }
            }
        }

        ///<summary>
        ///启动模拟进度任务
        ///</summary>
        private static async Task StartFakeProgressTask(IProgress<int> progress, CancellationToken cancellationToken)
        {
            try
            {
                int fakeProgress = 0;
                int increment = 3;

                while (fakeProgress < 85 && !cancellationToken.IsCancellationRequested)
                {
                    await Task.Delay(800, cancellationToken); //稍微慢一点，更真实
                    fakeProgress += increment;

                    //递减增量，模拟真实下载曲线
                    if (fakeProgress > 50)
                        increment = 2;
                    if (fakeProgress > 70)
                        increment = 1;

                    progress?.Report(Math.Min(fakeProgress, 85));
                    System.Diagnostics.Debug.WriteLine($"模拟进度: {fakeProgress}%");
                }
            }
            catch (OperationCanceledException)
            {
                System.Diagnostics.Debug.WriteLine("模拟进度任务已取消");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"模拟进度任务异常: {ex.Message}");
            }
        }

        ///<summary>
        ///安全更新进度条
        ///</summary>
        private static void UpdateProgressSafely(ProgressForm form, int percent)
        {
            if (form?.IsDisposed == false && form.IsHandleCreated)
            {
                try
                {
                    if (form.InvokeRequired)
                    {
                        form.BeginInvoke(new Action(() =>
                        {
                            if (!form.IsDisposed)
                                form.UpdateProgress(percent);
                        }));
                    }
                    else
                    {
                        form.UpdateProgress(percent);
                    }
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"更新进度异常: {ex.Message}");
                }
            }
        }

        ///<summary>
        ///安全关闭进度窗体
        ///</summary>
        private static void CloseProgressFormSafely(ProgressForm form)
        {
            if (form?.IsDisposed == false)
            {
                try
                {
                    if (form.InvokeRequired)
                    {
                        form.Invoke(new Action(() =>
                        {
                            if (!form.IsDisposed)
                            {
                                form.AllowClose();
                                form.Close();
                                form.Dispose();
                            }
                        }));
                    }
                    else
                    {
                        form.AllowClose();
                        form.Close();
                        form.Dispose();
                    }
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"关闭窗体异常: {ex.Message}");
                }
            }
        }

        ///<summary>
        ///显示错误消息
        ///</summary>
        private static void ShowErrorMessage(string message)
        {
            try
            {
                MessageBox.Show(message, "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"显示错误消息异常: {ex.Message}");
            }
        }

        ///<summary>
        ///处理 NAS 身份验证
        ///</summary>
        private static async Task HandleNasAuthentication(string updateURL)
        {
            try
            {
                //检查是否是 UNC 路径
                if (updateURL.StartsWith("\\\\") || updateURL.StartsWith("file://"))
                {
                    string uncPath = updateURL.Replace("file://", "").Replace("/", "\\");
                    if (!uncPath.StartsWith("\\\\"))
                        uncPath = "\\\\" + uncPath;

                    //从配置文件读取 NAS 凭据
                    var nasUsername = ConfigurationManager.AppSettings["NasUsername"];
                    var nasPassword = ConfigurationManager.AppSettings["NasPassword"];
                    var nasDomain = ConfigurationManager.AppSettings["NasDomain"] ?? "";

                    if (!string.IsNullOrEmpty(nasUsername) && !string.IsNullOrEmpty(nasPassword))
                    {
                        System.Diagnostics.Debug.WriteLine($"尝试连接到 NAS: {uncPath}");

                        //构建完整的用户名（包含域）
                        string fullUsername = string.IsNullOrEmpty(nasDomain) ? nasUsername : $"{nasDomain}\\{nasUsername}";

                        //建立网络连接
                        var result = ConnectToNetworkPath(uncPath, fullUsername, nasPassword);
                        if (result == 0)
                        {
                            System.Diagnostics.Debug.WriteLine("NAS 身份验证成功");
                        }
                        else
                        {
                            System.Diagnostics.Debug.WriteLine($"NAS 身份验证失败，错误代码: {result}");
                        }
                    }
                }
                else if (updateURL.StartsWith("http://") || updateURL.StartsWith("https://"))
                {
                    //HTTP 基本身份验证
                    var nasUsername = ConfigurationManager.AppSettings["NasUsername"];
                    var nasPassword = ConfigurationManager.AppSettings["NasPassword"];

                    if (!string.IsNullOrEmpty(nasUsername) && !string.IsNullOrEmpty(nasPassword))
                    {
                        System.Diagnostics.Debug.WriteLine("设置 HTTP 基本身份验证");

                        //设置默认网络凭据
                        var credentials = new NetworkCredential(nasUsername, nasPassword);
                        ServicePointManager.ServerCertificateValidationCallback = (sender, certificate, chain, sslPolicyErrors) => true;

                        //为 HTTP 请求设置默认凭据
                        System.Net.WebRequest.DefaultWebProxy.Credentials = credentials;
                    }
                }

                await Task.Delay(100); //给连接一点时间建立
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"NAS 身份验证异常: {ex.Message}");
                //不抛出异常，让程序继续尝试更新
            }
        }

        ///<summary>
        ///连接到网络路径
        ///</summary>
        private static int ConnectToNetworkPath(string networkPath, string username, string password)
        {
            try
            {
                //提取服务器路径（到第三个反斜杠为止）
                var parts = networkPath.Split(new char[] { '\\' }, StringSplitOptions.RemoveEmptyEntries);
                string serverPath = parts.Length >= 2 ? $"\\\\{parts[0]}\\{parts[1]}" : networkPath;

                var netResource = new NetResource
                {
                    Scope = 2,
                    Type = 1,
                    DisplayType = 3,
                    Usage = 1,
                    RemoteName = serverPath
                };

                return WNetAddConnection2(netResource, password, username, 0);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"连接网络路径异常: {ex.Message}");
                return -1;
            }
        }

        ///<summary>
        ///断开网络连接（程序退出时调用）
        ///</summary>
        public static void DisconnectNetworkPath(string networkPath)
        {
            try
            {
                if (networkPath.StartsWith("\\\\"))
                {
                    var parts = networkPath.Split(new char[] { '\\' }, StringSplitOptions.RemoveEmptyEntries);
                    string serverPath = parts.Length >= 2 ? $"\\\\{parts[0]}\\{parts[1]}" : networkPath;
                    WNetCancelConnection2(serverPath, 0, true);
                    System.Diagnostics.Debug.WriteLine($"已断开网络连接: {serverPath}");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"断开网络连接异常: {ex.Message}");
            }
        }

        #endregion
    }
}
