@echo off
setlocal enabledelayedexpansion

echo.
echo ===============================================================
echo                    EasyWork.Honor Quick Publish
echo ===============================================================
echo.
echo Please select publish method:
echo.
echo [1] Standard Publish (Batch)
echo [2] Advanced Publish (PowerShell)
echo [3] Auto Increment Version (PowerShell)
echo [4] Specify Version (PowerShell)
echo [5] Skip Build (PowerShell)
echo [0] Exit
echo.

set /p choice="Enter your choice (0-5): "

if "!choice!"=="1" (
    echo Starting standard publish...
    call OneClickPublish.bat
) else if "!choice!"=="2" (
    echo Starting advanced publish...
    powershell -ExecutionPolicy Bypass -File OneClickPublish.ps1
) else if "!choice!"=="3" (
    echo Starting auto increment version publish...
    powershell -ExecutionPolicy Bypass -File OneClickPublish.ps1 -IncrementVersion
) else if "!choice!"=="4" (
    set /p version="Enter version number (e.g. 1.2.3): "
    echo Starting specified version publish...
    powershell -ExecutionPolicy Bypass -File OneClickPublish.ps1 -Version "!version!"
) else if "!choice!"=="5" (
    echo Starting skip build publish...
    powershell -ExecutionPolicy Bypass -File OneClickPublish.ps1 -SkipBuild
) else if "!choice!"=="0" (
    echo Exiting...
    exit /b 0
) else (
    echo Invalid choice, please run script again
    pause
    exit /b 1
)

echo.
echo Publish completed!
pause
