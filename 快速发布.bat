@echo off
setlocal enabledelayedexpansion

REM 自动设置环境
if exist "%~dp0SetEnv.bat" (
    call "%~dp0SetEnv.bat"
)

echo.
echo ===============================================================
echo                    EasyWork.Honor Quick Publish
echo ===============================================================
echo.
echo Please select publish method:
echo.
echo [1] Standard Publish (Batch)
echo [2] Simple PowerShell Publish
echo [3] Auto Increment Version (PowerShell)
echo [4] Chinese Version Publish
echo [9] Setup Build Tools
echo [0] Exit
echo.

set /p choice="Enter your choice (0-4, 9): "

if "!choice!"=="1" (
    echo Starting standard publish...
    call "%~dp0OneClickPublish.bat"
) else if "!choice!"=="2" (
    echo Starting simple PowerShell publish...
    powershell -ExecutionPolicy Bypass -File "%~dp0SimplePublish.ps1"
) else if "!choice!"=="3" (
    echo Starting auto increment version publish...
    powershell -ExecutionPolicy Bypass -File "%~dp0SimplePublish.ps1" -IncrementVersion
) else if "!choice!"=="4" (
    echo Starting Chinese version publish...
    call "%~dp0一键发布-中文版.bat"
) else if "!choice!"=="9" (
    echo Setting up build tools...
    call "%~dp0SetupBuildTools.bat"
) else if "!choice!"=="0" (
    echo Exiting...
    exit /b 0
) else (
    echo Invalid choice, please run script again
    pause
    exit /b 1
)

echo.
echo Publish completed!
pause
