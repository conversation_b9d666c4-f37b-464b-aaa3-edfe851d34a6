@echo off
chcp 65001 >nul

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    EasyWork.Honor 快速发布                   ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.
echo 请选择发布方式：
echo.
echo [1] 标准发布 (批处理版)
echo [2] 高级发布 (PowerShell版)
echo [3] 递增版本发布 (PowerShell版)
echo [4] 指定版本发布 (PowerShell版)
echo [5] 跳过编译发布 (PowerShell版)
echo [0] 退出
echo.

set /p choice="请输入选择 (0-5): "

if "%choice%"=="1" (
    echo 启动标准发布...
    call OneClickPublish.bat
) else if "%choice%"=="2" (
    echo 启动高级发布...
    powershell -ExecutionPolicy Bypass -File OneClickPublish.ps1
) else if "%choice%"=="3" (
    echo 启动递增版本发布...
    powershell -ExecutionPolicy Bypass -File OneClickPublish.ps1 -IncrementVersion
) else if "%choice%"=="4" (
    set /p version="请输入版本号 (例如: 1.2.3): "
    echo 启动指定版本发布...
    powershell -ExecutionPolicy Bypass -File OneClickPublish.ps1 -Version "!version!"
) else if "%choice%"=="5" (
    echo 启动跳过编译发布...
    powershell -ExecutionPolicy Bypass -File OneClickPublish.ps1 -SkipBuild
) else if "%choice%"=="0" (
    echo 退出...
    exit /b 0
) else (
    echo 无效选择，请重新运行脚本
    pause
    exit /b 1
)

echo.
echo 发布完成！
pause
