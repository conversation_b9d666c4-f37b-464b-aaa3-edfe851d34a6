﻿using EasyWork.bll.Public;
using EasyWork.Honor.Service;
using EasyWork.Honor.Service.Print;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace EasyWork.Honor.PrintTool
{
    public partial class WXPsidWorkPrint : Form
    {
        string sn = "";
        string psid = "";
        string model = "";
        string code = "";
        string imei = "";
        string type = "";
        string incode = "";
        string error = "";
        string bsn = "";
        string endtime = "";
        string dep = "";
        string color="";
        string remarks = "";
        string bsnbake = "";
        string kaoTime = "";
        string ScriptText;
        public WXPsidWorkPrint()
        {
            InitializeComponent();
            printPreviewDialog1.Width = 100;
            printPreviewDialog1.Height = 300;
            //printDocument1.DefaultPageSettings.Landscape = true;
            this.printDocument1.OriginAtMargins = true;//启用页边距
            this.pageSetupDialog1.EnableMetric = true; //以毫米为单位    

            ScriptText = PrintService.Get_ZPLScript("主板过程小标", "ALL");
            tblr.Text = PublicService.GetConfigData("WXPsidWorkLR", string.Empty);
            tbud.Text = PublicService.GetConfigData("WXPsidWorkUD", string.Empty);
            if (tblr.Text == "")
            {
                tblr.Text = "0";
            }
            if (tbud.Text == "")
            {
                tbud.Text = "0";
            }
            tb_sn.Focus();
        }

         private void printDocument1_PrintPage(object sender, System.Drawing.Printing.PrintPageEventArgs e)
         {
            int x = int.Parse(tblr.Text.Trim());
            int y = int.Parse(tbud.Text.Trim());
            BarCode.Code128 _Code = new BarCode.Code128();
            //string sn = "AJVVUT1222002688";
            //string psid = "RBBKK15H1B3";
            //string model = "York-AN10C";
            //string code = "03031234-001";
            //string imei = "A00000D0094E5A";
            //string type = "整机拆主板";
            //string incode = "03031234-002";
            //string error = "不开机，就是不开机啊";
            //string bsn = "9RHQUT212J000994";

            string rf = sn.ToString().Substring(4, 2).ToUpper() == "GL"
                ? "-二返"
                : "";

            string yj = remarks.IndexOf("样机")>-1
             ? "-样机"
             : "";

            if (dep == "维修部门")
            {
                e.Graphics.DrawString("百沃维修主板工单流程卡"+rf+yj, new Font("宋体", (float)13), Brushes.Black, 90 + x, -90 + y);
            }
            else if (dep == "翻新部门")
            { 
                e.Graphics.DrawString("百沃维修翻新工单流程卡"+rf+yj, new Font("宋体", (float)13), Brushes.Black, 90 + x, -90 + y);
            }
            else
            {
                e.Graphics.DrawString("百沃维修PC工单流程卡" + rf + yj, new Font("宋体", (float)13), Brushes.Black, 90 + x, -90 + y);
            }

            //SN
            e.Graphics.DrawString("SN:", new Font("宋体", (float)10), Brushes.Black,-90+x, -84 + y);
            e.Graphics.DrawString(sn, new Font("宋体", (float)10), Brushes.Black,-70+x, -84 + y);           
            Bitmap sn_ime = _Code.GetCodeImage(sn, BarCode.Code128.Encode.Code128B);
            e.Graphics.DrawImage(sn_ime, -95 + x, -70 + y, 212, 30);

            //psid
            e.Graphics.DrawString("任务令:", new Font("宋体", (float)10), Brushes.Black, -90 + x, -39 + y);
            e.Graphics.DrawString(psid, new Font("宋体", (float)10), Brushes.Black, -40 + x, -39 + y);
            Bitmap psid_ime = _Code.GetCodeImage(psid, BarCode.Code128.Encode.Code128B);
            e.Graphics.DrawImage(psid_ime, -95 + x, -25 + y, 170, 26);

            //IMEI
            e.Graphics.DrawString("旧IMEI:", new Font("宋体", (float)10), Brushes.Black, 320 + x, -86 + y);
            e.Graphics.DrawString(imei, new Font("宋体", (float)10), Brushes.Black, 370 + x, -86 + y);
            Bitmap imei_ime = _Code.GetCodeImage(imei, BarCode.Code128.Encode.Code128B);
            e.Graphics.DrawImage(imei_ime, 250 + x, -73 + y, 222, 40);

            //目标编码
            e.Graphics.DrawString("目标编码:", new Font("宋体", (float)10), Brushes.Black, 100 + x, -37 + y);
            e.Graphics.DrawString(code, new Font("宋体", (float)10), Brushes.Black, 160 + x, -37 + y);
            Bitmap code_ime = _Code.GetCodeImage(code, BarCode.Code128.Encode.Code128B);
            e.Graphics.DrawImage(code_ime, 100 + x, -23 + y, 170, 26);

            //机型
            e.Graphics.DrawString("机型:", new Font("宋体", (float)10), Brushes.Black, 300 + x, -29 + y);
            e.Graphics.DrawString(model, new Font("宋体", (float)10), Brushes.Black, 340 + x, -29 + y);
            Bitmap model_ime = _Code.GetCodeImage(model, BarCode.Code128.Encode.Code128B);
            e.Graphics.DrawImage(model_ime, 300+ x, -15 + y, 170, 16);
      
            //来料故障
            e.Graphics.DrawString("来料故障:", new Font("宋体", (float)10), Brushes.Black, -93 + x, 7 + y);
            e.Graphics.DrawString(error, new Font("宋体", (float)10), Brushes.Black, -33 + x, 7 + y);


            //类型
            e.Graphics.DrawString("类型:", new Font("宋体", (float)8), Brushes.Black, 120 + x, -73 + y);
            e.Graphics.DrawString(type, new Font("宋体", (float)8), Brushes.Black, 150 + x, -73 + y);

            e.Graphics.DrawString("完工日期:", new Font("宋体", (float)8), Brushes.Black, 120 + x, -61 + y);
            e.Graphics.DrawString(DateTime.Parse(endtime).ToString("yyyy-MM-dd"), new Font("宋体", (float)8), Brushes.Black, 170 + x, -61 + y);

            e.Graphics.DrawString("烘烤日期:", new Font("宋体", (float)8), Brushes.Black, 120 + x, -48 + y);
            //e.Graphics.DrawString(bsnbake, new Font("宋体", (float)8), Brushes.Black, 170 + x, -48 + y);
            e.Graphics.DrawString(DateTime.Now.ToString("yyyy-MM-dd"), new Font("宋体", (float)8), Brushes.Black, 170 + x, -48 + y);

            //来料编码
            e.Graphics.DrawString("来料编码:", new Font("宋体", (float)9), Brushes.Black, 348 + x, 7 + y);
            e.Graphics.DrawString(incode, new Font("宋体", (float)9), Brushes.Black, 402 + x, 7 + y);


            if (dep=="翻新部门")
            {
                //大横线
                e.Graphics.DrawLine(Pens.Black, 485 + x, 5 + y, -95 + x, 5 + y);//1
                e.Graphics.DrawLine(Pens.Black, 485 + x, 210 + y, -95 + x, 210 + y);//2
                e.Graphics.DrawLine(Pens.Black, 485 + x, 22 + y, -95 + x, 22 + y);//3
                e.Graphics.DrawLine(Pens.Black, 485 + x, 34 + y, -95 + x, 34 + y);//4
                e.Graphics.DrawLine(Pens.Black, 485 + x, 50 + y, -95 + x, 50 + y);//5
                e.Graphics.DrawLine(Pens.Black, 485 + x, 66 + y, -95 + x, 66 + y);//6

                e.Graphics.DrawLine(Pens.Black, 485 + x, 81 + y, -95 + x, 81 + y);//7
                e.Graphics.DrawLine(Pens.Black, 215 + x, 95 + y, -95 + x, 95 + y);//8
                e.Graphics.DrawLine(Pens.Black, 215 + x, 110 + y, -95 + x, 110 + y);//9
                e.Graphics.DrawLine(Pens.Black, 215 + x, 124 + y, -95 + x, 124 + y);//10
                e.Graphics.DrawLine(Pens.Black, 215 + x, 138 + y, -95 + x, 138 + y);//11
                e.Graphics.DrawLine(Pens.Black, 215 + x, 152 + y, -95 + x, 152 + y);//12
                e.Graphics.DrawLine(Pens.Black, 215 + x, 166 + y, -95 + x, 166 + y);//13
                e.Graphics.DrawLine(Pens.Black, 215 + x, 180 + y, -95 + x, 180 + y);//14
                e.Graphics.DrawLine(Pens.Black, 215 + x, 194 + y, -95 + x, 194 + y);//14
                //e.Graphics.DrawLine(Pens.Black, 215 + x, 194 + y, -95 + x, 194 + y);//14




                //大竖线
                e.Graphics.DrawLine(Pens.Black, -95 + x, 5 + y, -95 + x, 210 + y);
                e.Graphics.DrawLine(Pens.Black, 485 + x, 5 + y, 485 + x, 210 + y);


                e.Graphics.DrawLine(Pens.Black, 350 + x, 5 + y, 350 + x, 22 + y); //第一行竖线


                //维修员
                e.Graphics.DrawString("维修次数", new Font("宋体", (float)8), Brushes.Black, -93 + x, 23 + y);
                e.Graphics.DrawString("确认故障", new Font("宋体", (float)8), Brushes.Black, -38 + x, 23 + y);
                e.Graphics.DrawString("维修方法", new Font("宋体", (float)8), Brushes.Black, 15 + x, 23 + y);
                e.Graphics.DrawString("维修员", new Font("宋体", (float)8), Brushes.Black, 75 + x, 23 + y);
                e.Graphics.DrawString("配件位号", new Font("宋体", (float)8), Brushes.Black, 160 + x, 23 + y);
                e.Graphics.DrawString("更换配件", new Font("宋体", (float)8), Brushes.Black, 330 + x, 23 + y);

                e.Graphics.DrawString("一次维修", new Font("宋体", (float)8), Brushes.Black, -93 + x, 38 + y);
                e.Graphics.DrawString("二次维修", new Font("宋体", (float)8), Brushes.Black, -93 + x, 53 + y);
                e.Graphics.DrawString("颜色: " + color, new Font("宋体", (float)11), Brushes.Black, 230 + x, 66 + y) ;

                e.Graphics.DrawLine(Pens.Black, -40 + x, 22 + y, -40 + x, 66 + y); //第1条竖线
                e.Graphics.DrawLine(Pens.Black, 10 + x, 22 + y, 10 + x, 66 + y); //第2条竖线
                e.Graphics.DrawLine(Pens.Black, 70 + x, 22 + y, 70 + x, 66 + y); //第3条竖线
                e.Graphics.DrawLine(Pens.Black, 115 + x, 22 + y, 115 + x, 66 + y); //第4条竖线
                e.Graphics.DrawLine(Pens.Black, 250 + x, 22 + y, 250 + x, 66 + y); //第5条竖线

           



                e.Graphics.DrawString("1.初检", new Font("宋体", (float)8), Brushes.Black, -93 + x, 83 + y);//第一排文字
                e.Graphics.DrawString("2.半擦", new Font("宋体", (float)8), Brushes.Black, -93 + x, 97 + y);//第一排文字
                e.Graphics.DrawString("3.拆机", new Font("宋体", (float)8), Brushes.Black, -93 + x, 112 + y);//第一排文字
                e.Graphics.DrawString("4.清胶", new Font("宋体", (float)8), Brushes.Black, -93 + x, 126 + y);//第一排文字
                e.Graphics.DrawString("5.TGMES录入", new Font("宋体", (float)6.7), Brushes.Black, -93 + x, 140 + y);//第一排文字
                e.Graphics.DrawString("6.CBT", new Font("宋体", (float)8), Brushes.Black, -93 + x, 154 + y);//第一排文字
                e.Graphics.DrawString("7.PT", new Font("宋体", (float)8), Brushes.Black, -93 + x, 168 + y);//第一排文字
                e.Graphics.DrawString("8.锁螺钉", new Font("宋体", (float)8), Brushes.Black, -93 + x, 182 + y);//第一排文字
                e.Graphics.DrawString("9.MMIBB", new Font("宋体", (float)8), Brushes.Black, -93 + x, 196 + y);//第一排文字

                e.Graphics.DrawString("工序", new Font("宋体", (float)8), Brushes.Black, -88 + x, 68 + y);//第一排文字
                e.Graphics.DrawString("工号", new Font("宋体", (float)8), Brushes.Black, -38 + x, 68 + y);//第二排文字
                e.Graphics.DrawString("不良故障", new Font("宋体", (float)8), Brushes.Black, 5 + x, 68 + y);//第三排文字


                e.Graphics.DrawString("10.老化", new Font("宋体", (float)8), Brushes.Black, 61 + x, 83 + y);//第四排文字
                e.Graphics.DrawString("11.MT", new Font("宋体", (float)8), Brushes.Black, 61 + x, 97 + y);//第四排文字
                e.Graphics.DrawString("12.贴背胶", new Font("宋体", (float)8), Brushes.Black, 61 + x, 112 + y);//第四排文字
                e.Graphics.DrawString("13.点胶/压合", new Font("宋体", (float)6.5), Brushes.Black, 61 + x, 126 + y);//第四排文字
                e.Graphics.DrawString("14.外观1", new Font("宋体", (float)8), Brushes.Black, 60 + x, 140 + y);//第四排文字
                e.Graphics.DrawString("15.升级改号", new Font("宋体", (float)6.7), Brushes.Black, 61 + x, 154 + y);//第四排文字
                e.Graphics.DrawString("16.PQC", new Font("宋体", (float)8), Brushes.Black, 61 + x, 168 + y);//第四排文字
                e.Graphics.DrawString("17.MC", new Font("宋体", (float)8), Brushes.Black, 61 + x, 182 + y);//第四排文字
                e.Graphics.DrawString("18.外观2", new Font("宋体", (float)8), Brushes.Black, 61 + x, 196 + y);//第四排文字

                e.Graphics.DrawString("工序", new Font("宋体", (float)8), Brushes.Black, 68 + x, 68 + y);//第四排文字
                e.Graphics.DrawString("工号", new Font("宋体", (float)8), Brushes.Black, 120 + x, 68 + y);//第五排文字
                e.Graphics.DrawString("不良故障", new Font("宋体", (float)8), Brushes.Black, 160 + x, 68 + y);//第六排文字


                e.Graphics.DrawLine(Pens.Black, -40 + x, 66 + y, -40 + x, 210 + y); //第1条竖线
                e.Graphics.DrawLine(Pens.Black, 0 + x, 66 + y, 0 + x, 210 + y); //第2条竖线
                e.Graphics.DrawLine(Pens.Black, 60 + x, 66 + y, 60 + x, 210 + y); //第3条竖线

                e.Graphics.DrawLine(Pens.Black, 115 + x, 66 + y, 115 + x, 210 + y); //第4条竖线
                e.Graphics.DrawLine(Pens.Black, 155 + x, 66 + y, 155 + x, 210 + y); //第5条竖线
                e.Graphics.DrawLine(Pens.Black, 215 + x, 66 + y, 215 + x, 210 + y); //第6条竖线

                e.Graphics.DrawString("单板号码:", new Font("宋体", (float)9), Brushes.Black, 230 + x, 82 + y);
                e.Graphics.DrawString(bsn, new Font("宋体", (float)9), Brushes.Black, 291 + x, 82 + y);
                Bitmap bsn_ime = _Code.GetCodeImage(bsn, BarCode.Code128.Encode.Code128B);
                e.Graphics.DrawImage(bsn_ime, 230 + x, 94 + y, 222, 20);

                //横线
                e.Graphics.DrawLine(Pens.Black, 485 + x, 116 + y, 215 + x, 116 + y);//15
                e.Graphics.DrawLine(Pens.Black, 485 + x, 130 + y, 215 + x, 130 + y);//16
                e.Graphics.DrawLine(Pens.Black, 485 + x, 145 + y, 215 + x, 145 + y);//17
                e.Graphics.DrawLine(Pens.Black, 485 + x, 195 + y, 215 + x, 195 + y);//18

                //报废
                e.Graphics.DrawString("来料报废□", new Font("宋体", (float)10), Brushes.Black, 220 + x, 116 + y);
                e.Graphics.DrawString("公告报废□", new Font("宋体", (float)10), Brushes.Black, 295 + x, 116 + y);
                e.Graphics.DrawString("维修报废□", new Font("宋体", (float)10), Brushes.Black, 370 + x, 116 + y);
                e.Graphics.DrawString("退库□", new Font("宋体", (float)10), Brushes.Black, 440 + x, 116 + y);

                e.Graphics.DrawLine(Pens.Black, 340 + x, 130 + y, 340 + x, 145 + y); //第1条竖线
                e.Graphics.DrawLine(Pens.Black, 320 + x, 195 + y, 320 + x, 210 + y); //第2条竖线

                e.Graphics.DrawString("申请人：", new Font("宋体", (float)10), Brushes.Black, 215 + x, 131 + y);
                e.Graphics.DrawString("申请日期：", new Font("宋体", (float)10), Brushes.Black, 341 + x, 131 + y);
                e.Graphics.DrawString("报废原因：", new Font("宋体", (float)10), Brushes.Black, 215 + x, 146 + y);
                e.Graphics.DrawString("审核人：", new Font("宋体", (float)10), Brushes.Black, 215 + x, 196 + y);
                e.Graphics.DrawString("审核意见：", new Font("宋体", (float)10), Brushes.Black, 321 + x, 196 + y);
            }
            else
            {
                //大横线
                e.Graphics.DrawLine(Pens.Black, 485 + x, 5 + y, -95 + x, 5 + y);//1
                e.Graphics.DrawLine(Pens.Black, 485 + x, 210 + y, -95 + x, 210 + y);//2
                e.Graphics.DrawLine(Pens.Black, 485 + x, 22 + y, -95 + x, 22 + y);//3
                e.Graphics.DrawLine(Pens.Black, 485 + x, 34 + y, -95 + x, 34 + y);//4
                e.Graphics.DrawLine(Pens.Black, 485 + x, 50 + y, -95 + x, 50 + y);//5
                e.Graphics.DrawLine(Pens.Black, 485 + x, 66 + y, -95 + x, 66 + y);//6
                e.Graphics.DrawLine(Pens.Black, 485 + x, 82 + y, -95 + x, 82 + y);//7
                e.Graphics.DrawLine(Pens.Black, 215 + x, 98 + y, -95 + x, 98 + y);//8
                e.Graphics.DrawLine(Pens.Black, 215 + x, 114 + y, -95 + x, 114 + y);//9
                e.Graphics.DrawLine(Pens.Black, 215 + x, 130 + y, -95 + x, 130 + y);//10
                e.Graphics.DrawLine(Pens.Black, 215 + x, 146 + y, -95 + x, 146 + y);//11
                e.Graphics.DrawLine(Pens.Black, 215 + x, 162 + y, -95 + x, 162 + y);//12
                e.Graphics.DrawLine(Pens.Black, 215 + x, 178 + y, -95 + x, 178 + y);//13
                e.Graphics.DrawLine(Pens.Black, 215 + x, 194 + y, -95 + x, 194 + y);//14

                e.Graphics.DrawLine(Pens.Black, 485 + x, 116 + y, 215 + x, 116 + y);//15
                e.Graphics.DrawLine(Pens.Black, 485 + x, 130 + y, 215 + x, 130 + y);//16
                e.Graphics.DrawLine(Pens.Black, 485 + x, 145 + y, 215 + x, 145 + y);//17
                e.Graphics.DrawLine(Pens.Black, 485 + x, 195 + y, 215 + x, 195 + y);//18


                //大竖线
                e.Graphics.DrawLine(Pens.Black, -95 + x, 5 + y, -95 + x, 210 + y);
                e.Graphics.DrawLine(Pens.Black, 485 + x, 5 + y, 485 + x, 210 + y);


                e.Graphics.DrawLine(Pens.Black, 350 + x, 5 + y, 350 + x, 22 + y); //第一行竖线


                //维修员
                e.Graphics.DrawString("维修次数", new Font("宋体", (float)8), Brushes.Black, -93 + x, 23 + y);
                e.Graphics.DrawString("确认故障", new Font("宋体", (float)8), Brushes.Black, -38 + x, 23 + y);
                e.Graphics.DrawString("维修方法", new Font("宋体", (float)8), Brushes.Black, 15 + x, 23 + y);
                e.Graphics.DrawString("维修员", new Font("宋体", (float)8), Brushes.Black, 75 + x, 23 + y);
                e.Graphics.DrawString("配件位号", new Font("宋体", (float)8), Brushes.Black, 160 + x, 23 + y);
                e.Graphics.DrawString("更换配件", new Font("宋体", (float)8), Brushes.Black, 330 + x, 23 + y);

                e.Graphics.DrawString("一次维修", new Font("宋体", (float)8), Brushes.Black, -93 + x, 38 + y);
                e.Graphics.DrawString("二次维修", new Font("宋体", (float)8), Brushes.Black, -93 + x, 53 + y);
                e.Graphics.DrawString("三次维修", new Font("宋体", (float)8), Brushes.Black, -93 + x, 68 + y);


                e.Graphics.DrawLine(Pens.Black, -40 + x, 22 + y, -40 + x, 82 + y); //第1条竖线
                e.Graphics.DrawLine(Pens.Black, 10 + x, 22 + y, 10 + x, 82 + y); //第2条竖线
                e.Graphics.DrawLine(Pens.Black, 70 + x, 22 + y, 70 + x, 82 + y); //第3条竖线
                e.Graphics.DrawLine(Pens.Black, 115 + x, 22 + y, 115 + x, 82 + y); //第4条竖线
                e.Graphics.DrawLine(Pens.Black, 250 + x, 22 + y, 250 + x, 82 + y); //第5条竖线


                if (dep=="维修部门")
                {
                    //工序
                    e.Graphics.DrawString("工序", new Font("宋体", (float)8), Brushes.Black, -88 + x, 85 + y);//第一排文字
                    e.Graphics.DrawString("1.初检", new Font("宋体", (float)8), Brushes.Black, -93 + x, 101 + y);//第一排文字
                    e.Graphics.DrawString("2.半擦", new Font("宋体", (float)8), Brushes.Black, -93 + x, 117 + y);//第一排文字
                    e.Graphics.DrawString("3.拆机", new Font("宋体", (float)8), Brushes.Black, -93 + x, 133 + y);//第一排文字
                    e.Graphics.DrawString("4.TGMES录入", new Font("宋体", (float)6.7), Brushes.Black, -93 + x, 149 + y);//第一排文字
                    e.Graphics.DrawString("5.CBT", new Font("宋体", (float)8), Brushes.Black, -93 + x, 165 + y);//第一排文字
                    e.Graphics.DrawString("6.装机", new Font("宋体", (float)8), Brushes.Black, -93 + x, 181 + y);//第一排文字
                    e.Graphics.DrawString("7.PT", new Font("宋体", (float)8), Brushes.Black, -93 + x, 197 + y);//第一排文字


                    e.Graphics.DrawString("工号", new Font("宋体", (float)8), Brushes.Black, -38 + x, 85 + y);//第二排文字
                    e.Graphics.DrawString("不良故障", new Font("宋体", (float)8), Brushes.Black, 5 + x, 85 + y);//第三排文字

                    e.Graphics.DrawString("工序", new Font("宋体", (float)8), Brushes.Black, 68 + x, 85 + y);//第四排文字
                    e.Graphics.DrawString("8.MMIBB", new Font("宋体", (float)8), Brushes.Black, 61 + x, 101 + y);//第四排文字
                    e.Graphics.DrawString("9.老化", new Font("宋体", (float)8), Brushes.Black, 61 + x, 116 + y);//第四排文字
                    e.Graphics.DrawString("10.升级写号", new Font("宋体", (float)7), Brushes.Black, 61 + x, 132 + y);//第四排文字
                    e.Graphics.DrawString("11.PQC", new Font("宋体", (float)8), Brushes.Black, 61 + x, 147 + y);//第四排文字
                    e.Graphics.DrawString("12.MC", new Font("宋体", (float)8), Brushes.Black, 60 + x, 165 + y);//第四排文字
                    e.Graphics.DrawString("13.拆机", new Font("宋体", (float)8), Brushes.Black, 61 + x, 181 + y);//第四排文字
                    e.Graphics.DrawString("14.外观", new Font("宋体", (float)8), Brushes.Black, 61 + x, 197 + y);//第四排文字

                    e.Graphics.DrawString("工号", new Font("宋体", (float)8), Brushes.Black, 120 + x, 85 + y);//第五排文字
                    e.Graphics.DrawString("不良故障", new Font("宋体", (float)8), Brushes.Black, 160 + x, 85 + y);//第六排文字
                }
                else
                {
                    //工序
                    e.Graphics.DrawString("工序", new Font("宋体", (float)8), Brushes.Black, -88 + x, 85 + y);//第一排文字
                    e.Graphics.DrawString("1.NFF", new Font("宋体", (float)8), Brushes.Black, -93 + x, 101 + y);//第一排文字
                    e.Graphics.DrawString("2.TGMES录入/PT", new Font("宋体", (float)5.5), Brushes.Black, -93 + x, 117 + y);//第一排文字
                    e.Graphics.DrawString("3.外观/组装", new Font("宋体", (float)7), Brushes.Black, -93 + x, 133 + y);//第一排文字
                    e.Graphics.DrawString("4.镜像下载", new Font("宋体", (float)7), Brushes.Black, -93 + x, 149 + y);//第一排文字
                    e.Graphics.DrawString("5.BIOS升级", new Font("宋体", (float)7), Brushes.Black, -93 + x, 165 + y);//第一排文字
                    e.Graphics.DrawString("6.Prepar", new Font("宋体", (float)8), Brushes.Black, -93 + x, 181 + y);//第一排文字
                    e.Graphics.DrawString("7.老化", new Font("宋体", (float)8), Brushes.Black, -93 + x, 197 + y);//第一排文字


                    e.Graphics.DrawString("工号", new Font("宋体", (float)8), Brushes.Black, -38 + x, 85 + y);//第二排文字
                    e.Graphics.DrawString("不良故障", new Font("宋体", (float)8), Brushes.Black, 5 + x, 85 + y);//第三排文字

                    e.Graphics.DrawString("工序", new Font("宋体", (float)8), Brushes.Black, 68 + x, 85 + y);//第四排文字
                    e.Graphics.DrawString("8.FFT", new Font("宋体", (float)8), Brushes.Black, 61 + x, 101 + y);//第四排文字
                    e.Graphics.DrawString("9.CW/MC", new Font("宋体", (float)8), Brushes.Black, 61 + x, 116 + y);//第四排文字
                    e.Graphics.DrawString("10.PQC", new Font("宋体", (float)7), Brushes.Black, 61 + x, 132 + y);//第四排文字
                    e.Graphics.DrawString("11.OQC", new Font("宋体", (float)8), Brushes.Black, 61 + x, 147 + y);//第四排文字
                    e.Graphics.DrawString("12.拆机", new Font("宋体", (float)8), Brushes.Black, 60 + x, 165 + y);//第四排文字
                    e.Graphics.DrawString("13.外观2", new Font("宋体", (float)8), Brushes.Black, 61 + x, 181 + y);//第四排文字
                    e.Graphics.DrawString("", new Font("宋体", (float)8), Brushes.Black, 61 + x, 197 + y);//第四排文字

                    e.Graphics.DrawString("工号", new Font("宋体", (float)8), Brushes.Black, 120 + x, 85 + y);//第五排文字
                    e.Graphics.DrawString("不良故障", new Font("宋体", (float)8), Brushes.Black, 160 + x, 85 + y);//第六排文字

                }



                e.Graphics.DrawLine(Pens.Black, -40 + x, 82 + y, -40 + x, 210 + y); //第1条竖线
                e.Graphics.DrawLine(Pens.Black, 0 + x, 82 + y, 0 + x, 210 + y); //第2条竖线
                e.Graphics.DrawLine(Pens.Black, 60 + x, 82 + y, 60 + x, 210 + y); //第3条竖线

                e.Graphics.DrawLine(Pens.Black, 115 + x, 82 + y, 115 + x, 210 + y); //第4条竖线
                e.Graphics.DrawLine(Pens.Black, 155 + x, 82 + y, 155 + x, 210 + y); //第5条竖线
                e.Graphics.DrawLine(Pens.Black, 215 + x, 82 + y, 215 + x, 210 + y); //第6条竖线

                e.Graphics.DrawString("单板号码:", new Font("宋体", (float)9), Brushes.Black, 230 + x, 82 + y);
                e.Graphics.DrawString(bsn, new Font("宋体", (float)9), Brushes.Black, 291 + x, 82 + y);
                Bitmap bsn_ime = _Code.GetCodeImage(bsn, BarCode.Code128.Encode.Code128B);
                e.Graphics.DrawImage(bsn_ime, 230 + x, 94 + y, 222, 20);



                //报废
                e.Graphics.DrawString("来料报废□", new Font("宋体", (float)10), Brushes.Black, 220 + x, 116 + y);
                e.Graphics.DrawString("公告报废□", new Font("宋体", (float)10), Brushes.Black, 295 + x, 116 + y);
                e.Graphics.DrawString("维修报废□", new Font("宋体", (float)10), Brushes.Black, 370 + x, 116 + y);
                e.Graphics.DrawString("退库□", new Font("宋体", (float)10), Brushes.Black, 440 + x, 116 + y);

                e.Graphics.DrawLine(Pens.Black, 340 + x, 130 + y, 340 + x, 145 + y); //第1条竖线
                e.Graphics.DrawLine(Pens.Black, 320 + x, 195 + y, 320 + x, 210 + y); //第2条竖线

                e.Graphics.DrawString("申请人：", new Font("宋体", (float)10), Brushes.Black, 215 + x, 131 + y);
                e.Graphics.DrawString("申请日期：", new Font("宋体", (float)10), Brushes.Black, 341 + x, 131 + y);
                e.Graphics.DrawString("报废原因：", new Font("宋体", (float)10), Brushes.Black, 215 + x, 146 + y);
                e.Graphics.DrawString("审核人：", new Font("宋体", (float)10), Brushes.Black, 215 + x, 196 + y);
                e.Graphics.DrawString("审核意见：", new Font("宋体", (float)10), Brushes.Black, 321 + x, 196 + y);
            }

          




        }

        private void bt_Print_Click(object sender, EventArgs e)
        {
            //var targetDate = DateTime.Now.ToString("yyyy-MM-dd"); //获取页面上烘烤日期文本框值
            string sql = ck_sn.Checked
                 ? "select Psid,PhoneModel,InitialCode,TargetCode,type,Old_IMEI,SN,VeneerCode,OriginalFault,location,outtype,endtime,Department,color,CompleteTime,remarks,printgongdan,CreateTime from mes_honor.dbo.wxmes where veneercode='" + tb_sn.Text.Trim() + "' order by id desc"
                 : "select Psid,PhoneModel,InitialCode,TargetCode,type,Old_IMEI,SN,VeneerCode,OriginalFault,location,outtype,endtime,Department,color,CompleteTime,remarks,printgongdan,CreateTime from mes_honor.dbo.wxmes where sn='" + tb_sn.Text.Trim() + "' order by id desc";
            DataTable dt = SqlHelper.Query(sql).Tables[0];
            if (dt.Rows.Count == 0)
            {
                MessageBox.Show("系统没有该SN/单板数据");
                return;
            }
            if (!new[] { "", "FenPei", "WX" }.Contains(dt.Rows[0]["location"].ToString()))
            {
                MessageBox.Show("该SN、单板不在可打印工单状态");
                return;
            }

            sn = dt.Rows[0]["sn"].ToString();
            psid = dt.Rows[0]["psid"].ToString();
            model = dt.Rows[0]["phonemodel"].ToString();
            code = dt.Rows[0]["targetcode"].ToString();
            imei = dt.Rows[0]["old_imei"].ToString();
            type = dt.Rows[0]["type"].ToString();
            incode = dt.Rows[0]["initialcode"].ToString();
            error = dt.Rows[0]["originalfault"].ToString();
            bsn = dt.Rows[0]["veneercode"].ToString();
            //endtime = dt.Rows[0]["CompleteTime"].ToString();
            endtime = dt.Rows[0]["endtime"].ToString();
            dep = dt.Rows[0]["Department"].ToString();
            color = dt.Rows[0]["color"].ToString();
            remarks = dt.Rows[0]["remarks"].ToString();
            kaoTime = dt.Rows[0]["CreateTime"].ToString();


            if (endtime == "")
            {
                MessageBox.Show("请先分配部门");
                return;
            }

            //sn = "AYYKVB1804003923";
            //psid = "RBBKK1BN1B1";
            //model = "ElizabethS-AN00B";
            //code = "03034DVG";
            //imei = "A00000D0F2157A";
            //type = "整机-主板";
            //incode = "51096PGJ";
            //error = "3234 其他";
            //bsn = "9VAHVB217A030759";
            //endtime = DateTime.Now.ToString("yyyy-MM-dd");
            //dep = "PC主板";
            //bsnbake = targetDate;

            //if (dt.Rows[0]["printgongdan"].ToString() == "1")
            //{
            //    string sqllog = "select CreateTime from mes_honor.dbo.wxmes_log where location='工单打印' and psid='" + psid + "' and veneercode='" + bsn + "' order by id asc";
            //    DataTable dtt = SqlHelper.Query(sqllog).Tables[0];
            //    if (sqllog.Count() > 0)
            //    {
            //        bsnbake = DateTime.Parse(dtt.Rows[0]["CreateTime"].ToString()).ToString("yyyy-MM-dd");
            //    }
            //}

            bsnbake = DateTime.Now.ToString("yyyy-MM-dd");

            if (bsnbake == "")
            {
                bsnbake = DateTime.Now.ToString("yyyy-MM-dd");
            }



            //printPreviewDialog1.Document = printDocument1;
            //printPreviewDialog1.ShowDialog();
            //return;

            if (rb_all.Checked||rb_work.Checked||rb_all2.Enabled)
            {
                printDocument1.Print();
            }

            if (rb_all.Checked||rb_xb.Checked||rb_all2.Checked)
            {
                string text = ScriptText;
                for (int j = 0; j < 5; j++)
                {
                    text = text.Replace("$MEID$", imei.ToUpper().Trim());
                }
                ZPLPrint zb = new ZPLPrint();
                zb.ZPL_Print(text, "USB\\VID_0FE6&PID_811E\\5&1F95C3CA&0&10");
                zb.ZPL_Print(text, "USB\\VID_0A5F&PID_00AB\\JJL063834");
                //  zb.ZPL_Print(text);

                if (rb_all2.Checked)
                {
                    zb.ZPL_Print(text, "USB\\VID_0FE6&PID_811E\\5&1F95C3CA&0&10");
                    zb.ZPL_Print(text, "USB\\VID_0A5F&PID_00AB\\JJL063834");
                }
            }



            UpdatePrintGongDan(psid);
            InsertLog(psid, model, incode, code, dt.Rows[0]["outtype"].ToString(), imei, sn, bsn, error, type);
            tb_sn.Text = "";
            tb_sn.Focus();
        }

        public void UpdatePrintGongDan(string psid)
        {
            string locationValue = dep == "改制部门" ? "Assemble" : "待分配";
            string stateValue = dep == "改制部门" ? "make" : "Receive";
            string sql = ck_sn.Checked
                ? "update mes_honor.dbo.wxmes set PrintGongDan='1',location='" + locationValue + "',state='" + stateValue + "' where veneercode='" + tb_sn.Text.Trim() + "' and psid='" + psid + "'"
                : "update mes_honor.dbo.wxmes set PrintGongDan='1',location='" + locationValue + "',state='" + stateValue + "' where sn='" + tb_sn.Text.Trim() + "' and psid='" + psid + "'";
            SqlHelper.ExecuteSql(sql);
        }

        public void InsertLog(string psid,string phonemodel,string incode,string outcode,string outtype,string old_imei,string sn,string veneercode,string fault,string type)
        {
            string sql = "insert into mes_honor.dbo.wxmes_log(psid,phonemodel,initialcode,targetcode,outtype,old_imei,sn,veneercode,originalfault,location,approve,type,operatetype,createbyid,createtime) values('"+psid+ "','"+phonemodel+ "','"+incode+ "','"+outcode+ "','"+outtype+ "','"+old_imei+ "','"+sn+ "','"+veneercode+ "','"+fault+ "','工单打印','1','"+type+ "','PsidPlan','"+Login.usercode+"',getdate())";
            SqlHelper.ExecuteSql(sql);
        }

        private void tblr_Validated(object sender, EventArgs e)
        {
            if (!int.TryParse(tblr.Text.Trim(),out int _dec))
            {
                tblr.Text = "0";
            }
            PublicService.WriteConfigData("WXPsidWorkLR",tblr.Text.Trim());
        }

        private void tbud_Validated(object sender, EventArgs e)
        {
            if (!int.TryParse(tbud.Text.Trim(), out int _dec))
            {
                tbud.Text = "0";
            }
            PublicService.WriteConfigData("WXPsidWorkUD", tbud.Text.Trim());
        }    

        private void tb_sn_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode==Keys.Enter)
            {
                bt_Print_Click(null,null);
            }
        }
    }
}
