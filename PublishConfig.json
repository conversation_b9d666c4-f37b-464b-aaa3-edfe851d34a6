{"project": {"name": "EasyWork.Honor", "projectFile": "EasyWork.Honor.csproj", "nuspecFile": "EasyWork.Honor.nuspec", "buildConfig": "Release", "platform": "Any CPU"}, "nas": {"path": "\\\\***********\\public\\00A-IT信息化\\小狗呀\\install", "username": "read", "password": "123456"}, "version": {"autoIncrement": false, "incrementType": "patch"}, "build": {"skipIfExists": false, "cleanBeforeBuild": true, "verboseOutput": false}, "deployment": {"backupExisting": true, "verifyAfterDeploy": true, "retryCount": 3}, "logging": {"level": "INFO", "keepLogDays": 30}}