﻿using EasyWork.Model;
using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace EasyWork.Honor.Service.WXMES
{
    public class WXMESPublic
    {
        /// <summary>
        /// 过站系统过站时的存储过程
        /// </summary>
        /// <param name="control">过站系统的list</param>
        /// <returns>返回是否保存成功</returns>
        public static string GZProcedure(ControlProcessList control)
        {
            SqlParameter[] pre = new SqlParameter[]{
                    new SqlParameter("@id",control.id),
                     new SqlParameter("@ErrNumber",control.errnumber),
                      new SqlParameter("@approve",control.approve),
                       new SqlParameter("@username",control.username),
                        new SqlParameter("@job",control.job),
                         new SqlParameter("@location",control.location),
                          new SqlParameter("@Newimei",control.newimei),
                           new SqlParameter("@wxname",control.wxname),
                            new SqlParameter("@remarks",control.remarks),
                             new SqlParameter("@other",control.other),
                             new SqlParameter("@battery",control.battery),
                              new SqlParameter("@psid",control.psid),
                              new SqlParameter("@phonetype",control.phonetype),
                              new SqlParameter("@targetcode",control.targetcode),
                              new SqlParameter("@type",control.type),
                              new SqlParameter("@old_imei",control.old_imei),
                              new SqlParameter("@sn",control.sn),
                              new SqlParameter("@veneercode",control.veneercode),
                              new SqlParameter("@initialcode",control.initialcode),
                              new SqlParameter("@wxtype",control.wxtype),
                              new SqlParameter("@inname",control.inname),
                              new SqlParameter("@intime",control.intime)
            };
            return SqlHelperMES.RunProcedurestring("pro_WXGZ", pre);
        }
    }
}
