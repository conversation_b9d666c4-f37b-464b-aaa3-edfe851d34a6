using System;
using System.IO;
using System.Security.Cryptography;
using System.Text;
using System.Windows.Forms;

namespace EasyWork.Honor
{
    /// <summary>
    /// RELEASES文件生成工具
    /// </summary>
    public static class ReleasesFileGenerator
    {
        /// <summary>
        /// 为指定目录生成RELEASES文件
        /// </summary>
        /// <param name="directory">包含.nupkg文件的目录</param>
        /// <returns>是否成功生成</returns>
        public static bool GenerateReleasesFile(string directory)
        {
            try
            {
                if (!Directory.Exists(directory))
                {
                    MessageBox.Show($"目录不存在: {directory}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return false;
                }

                var nupkgFiles = Directory.GetFiles(directory, "*.nupkg");
                if (nupkgFiles.Length == 0)
                {
                    MessageBox.Show($"目录中没有找到.nupkg文件: {directory}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return false;
                }

                var releasesPath = Path.Combine(directory, "RELEASES");
                var releasesContent = new StringBuilder();

                foreach (var nupkgFile in nupkgFiles)
                {
                    var fileInfo = new FileInfo(nupkgFile);
                    var fileName = fileInfo.Name;
                    var fileSize = fileInfo.Length;
                    var sha1Hash = CalculateSHA1(nupkgFile);

                    // 格式: SHA1 文件名 文件大小
                    releasesContent.AppendLine($"{sha1Hash} {fileName} {fileSize}");
                    
                    System.Diagnostics.Debug.WriteLine($"添加到RELEASES: {sha1Hash} {fileName} {fileSize}");
                }

                // 写入RELEASES文件（UTF-8编码，无BOM）
                File.WriteAllText(releasesPath, releasesContent.ToString(), new UTF8Encoding(false));

                MessageBox.Show($"RELEASES文件生成成功!\n路径: {releasesPath}\n包含 {nupkgFiles.Length} 个包", 
                    "成功", MessageBoxButtons.OK, MessageBoxIcon.Information);

                return true;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"生成RELEASES文件失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return false;
            }
        }

        /// <summary>
        /// 计算文件的SHA1哈希值
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>SHA1哈希值（大写）</returns>
        private static string CalculateSHA1(string filePath)
        {
            using (var sha1 = SHA1.Create())
            using (var stream = File.OpenRead(filePath))
            {
                var hash = sha1.ComputeHash(stream);
                return BitConverter.ToString(hash).Replace("-", "").ToUpperInvariant();
            }
        }

        /// <summary>
        /// 验证RELEASES文件格式
        /// </summary>
        /// <param name="releasesFilePath">RELEASES文件路径</param>
        /// <returns>验证结果</returns>
        public static ReleasesValidationResult ValidateReleasesFile(string releasesFilePath)
        {
            var result = new ReleasesValidationResult();

            try
            {
                if (!File.Exists(releasesFilePath))
                {
                    result.IsValid = false;
                    result.ErrorMessage = "RELEASES文件不存在";
                    return result;
                }

                var fileInfo = new FileInfo(releasesFilePath);
                if (fileInfo.Length == 0)
                {
                    result.IsValid = false;
                    result.ErrorMessage = "RELEASES文件为空";
                    return result;
                }

                var content = File.ReadAllText(releasesFilePath);
                if (string.IsNullOrWhiteSpace(content))
                {
                    result.IsValid = false;
                    result.ErrorMessage = "RELEASES文件内容为空或只包含空白字符";
                    return result;
                }

                var lines = content.Split(new[] { '\r', '\n' }, StringSplitOptions.RemoveEmptyEntries);
                result.LineCount = lines.Length;

                for (int i = 0; i < lines.Length; i++)
                {
                    var line = lines[i].Trim();
                    if (string.IsNullOrEmpty(line)) continue;

                    var parts = line.Split(' ');
                    if (parts.Length < 3)
                    {
                        result.IsValid = false;
                        result.ErrorMessage = $"第{i + 1}行格式错误: 应该包含至少3个部分 (SHA1 文件名 大小)";
                        return result;
                    }

                    var sha1 = parts[0];
                    var fileName = parts[1];
                    var sizeStr = parts[2];

                    // 验证SHA1
                    if (sha1.Length != 40 || !System.Text.RegularExpressions.Regex.IsMatch(sha1, "^[a-fA-F0-9]+$"))
                    {
                        result.IsValid = false;
                        result.ErrorMessage = $"第{i + 1}行SHA1格式错误: {sha1}";
                        return result;
                    }

                    // 验证文件大小
                    if (!long.TryParse(sizeStr, out long size))
                    {
                        result.IsValid = false;
                        result.ErrorMessage = $"第{i + 1}行文件大小格式错误: {sizeStr}";
                        return result;
                    }

                    result.Entries.Add(new ReleasesEntry
                    {
                        SHA1 = sha1,
                        FileName = fileName,
                        FileSize = size
                    });
                }

                result.IsValid = true;
                result.ErrorMessage = "RELEASES文件格式正确";
                return result;
            }
            catch (Exception ex)
            {
                result.IsValid = false;
                result.ErrorMessage = $"验证RELEASES文件异常: {ex.Message}";
                return result;
            }
        }
    }

    /// <summary>
    /// RELEASES文件验证结果
    /// </summary>
    public class ReleasesValidationResult
    {
        public bool IsValid { get; set; }
        public string ErrorMessage { get; set; }
        public int LineCount { get; set; }
        public System.Collections.Generic.List<ReleasesEntry> Entries { get; set; } = new System.Collections.Generic.List<ReleasesEntry>();
    }

    /// <summary>
    /// RELEASES文件条目
    /// </summary>
    public class ReleasesEntry
    {
        public string SHA1 { get; set; }
        public string FileName { get; set; }
        public long FileSize { get; set; }
    }
}
