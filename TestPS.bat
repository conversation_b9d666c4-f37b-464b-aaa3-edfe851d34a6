@echo off
echo Testing PowerShell execution...
echo Current directory: %CD%
echo Script directory: %~dp0
echo.

echo Testing PowerShell file existence...
if exist "%~dp0SimplePublish.ps1" (
    echo SimplePublish.ps1 found
) else (
    echo SimplePublish.ps1 NOT found
)

echo.
echo Testing PowerShell execution...
powershell -ExecutionPolicy Bypass -Command "Write-Host 'PowerShell is working'; Get-Location"

echo.
echo Testing PowerShell file execution...
powershell -ExecutionPolicy Bypass -File "%~dp0SimplePublish.ps1" -Version "1.0.0"

pause
