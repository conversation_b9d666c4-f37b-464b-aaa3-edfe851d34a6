@echo off
setlocal enabledelayedexpansion

echo.
echo ===============================================================
echo                    EasyWork.Honor 一键发布工具
echo                            版本 1.0.0
echo ===============================================================
echo.

REM 配置参数
set PROJECT_NAME=EasyWork.Honor
set PROJECT_FILE=%PROJECT_NAME%.csproj
set NUSPEC_FILE=%PROJECT_NAME%.nuspec
set BUILD_CONFIG=Release
set PLATFORM=AnyCPU
set NAS_PATH=\\172.20.0.20\public\00A-IT信息化\小狗呀\install
set NAS_USER=read
set NAS_PASS=123456

REM 工作目录
set WORK_DIR=%~dp0
set BUILD_DIR=%WORK_DIR%bin\%BUILD_CONFIG%
set TEMP_DIR=%WORK_DIR%temp_publish
set LOG_FILE=%WORK_DIR%publish_log_%date:~0,4%%date:~5,2%%date:~8,2%_%time:~0,2%%time:~3,2%%time:~6,2%.txt

REM 创建日志文件
echo [%date% %time%] 开始一键发布流程 > "%LOG_FILE%"

echo [1/8] 环境检查...
call :log "开始环境检查"

REM 检查必要工具
where msbuild >nul 2>&1
if errorlevel 1 (
    call :error "MSBuild 未找到，请确保已安装 Visual Studio 或 Build Tools"
    goto :end
)

where nuget >nul 2>&1
if errorlevel 1 (
    call :error "NuGet CLI 未找到，请下载并添加到 PATH 环境变量"
    goto :end
)

where Squirrel >nul 2>&1
if errorlevel 1 (
    call :error "Squirrel 未找到，请安装 Squirrel.Windows"
    goto :end
)

REM 检查项目文件
if not exist "%PROJECT_FILE%" (
    call :error "项目文件 %PROJECT_FILE% 未找到"
    goto :end
)

if not exist "%NUSPEC_FILE%" (
    call :error "NuSpec文件 %NUSPEC_FILE% 未找到"
    goto :end
)

call :success "环境检查完成"

echo [2/8] 版本号处理...
call :log "开始版本号处理"

REM 从AssemblyInfo.cs读取当前版本号
for /f "tokens=2 delims=[]" %%i in ('findstr "AssemblyVersion" Properties\AssemblyInfo.cs') do (
    set ASSEMBLY_VERSION=%%i
)
set ASSEMBLY_VERSION=%ASSEMBLY_VERSION:"=%
set ASSEMBLY_VERSION=%ASSEMBLY_VERSION: =%

REM 提取版本号（去掉最后的.0）
for /f "tokens=1,2,3,4 delims=." %%a in ("%ASSEMBLY_VERSION%") do (
    set VERSION=%%a.%%b.%%c
)

echo 当前版本: %VERSION%
call :log "当前版本: %VERSION%"

REM 询问是否递增版本号
set /p INCREMENT_VERSION="是否递增版本号? (y/n, 默认n): "
if /i "%INCREMENT_VERSION%"=="y" (
    call :increment_version
)

call :success "版本号: %VERSION%"

echo [3/8] 清理旧文件...
call :log "开始清理旧文件"

if exist "%TEMP_DIR%" rmdir /s /q "%TEMP_DIR%"
mkdir "%TEMP_DIR%"

if exist "%PROJECT_NAME%.*.nupkg" del "%PROJECT_NAME%.*.nupkg"

call :success "清理完成"

echo [4/8] 编译项目...
call :log "开始编译项目"

msbuild "%PROJECT_FILE%" /p:Configuration=%BUILD_CONFIG% /p:Platform="%PLATFORM%" /t:Clean,Build /v:minimal
if errorlevel 1 (
    call :error "项目编译失败"
    goto :end
)

call :success "编译完成"

echo [5/8] 生成NuGet包...
call :log "开始生成NuGet包"

REM 更新nuspec文件中的版本号
powershell -Command "(Get-Content '%NUSPEC_FILE%') -replace '<version>.*</version>', '<version>%VERSION%</version>' | Set-Content '%NUSPEC_FILE%'"

nuget pack "%NUSPEC_FILE%"
if errorlevel 1 (
    call :error "NuGet包生成失败"
    goto :end
)

set NUPKG_FILE=%PROJECT_NAME%.%VERSION%.nupkg
if not exist "%NUPKG_FILE%" (
    call :error "NuGet包文件 %NUPKG_FILE% 未找到"
    goto :end
)

call :success "NuGet包生成完成: %NUPKG_FILE%"

echo [6/8] Squirrel打包...
call :log "开始Squirrel打包"

Squirrel --releasify "%NUPKG_FILE%" --releaseDir="%TEMP_DIR%"
if errorlevel 1 (
    call :error "Squirrel打包失败"
    goto :end
)

call :success "Squirrel打包完成"

echo [7/8] 连接NAS服务器...
call :log "开始连接NAS服务器"

REM 断开可能存在的连接
net use "%NAS_PATH%" /delete >nul 2>&1

REM 建立新连接
net use "%NAS_PATH%" "%NAS_PASS%" /user:"%NAS_USER%"
if errorlevel 1 (
    call :error "连接NAS服务器失败"
    goto :end
)

call :success "NAS服务器连接成功"

echo [8/8] 部署到NAS...
call :log "开始部署到NAS"

REM 备份现有文件
if exist "%NAS_PATH%\RELEASES" (
    copy "%NAS_PATH%\RELEASES" "%NAS_PATH%\RELEASES.backup.%date:~0,4%%date:~5,2%%date:~8,2%" >nul
    call :log "已备份现有RELEASES文件"
)

REM 复制新文件
xcopy "%TEMP_DIR%\*" "%NAS_PATH%\" /E /Y
if errorlevel 1 (
    call :error "部署到NAS失败"
    goto :end
)

call :success "部署完成"

echo.
echo ===============================================================
echo                        发布成功！
echo.
echo  版本: %VERSION%
echo  部署位置: %NAS_PATH%
echo  日志文件: %LOG_FILE%
echo ===============================================================
echo.

call :log "发布流程完成"
goto :cleanup

:increment_version
REM 递增版本号逻辑
for /f "tokens=1,2,3 delims=." %%a in ("%VERSION%") do (
    set MAJOR=%%a
    set MINOR=%%b
    set PATCH=%%c
)
set /a PATCH+=1
set VERSION=%MAJOR%.%MINOR%.%PATCH%
set NEW_ASSEMBLY_VERSION=%VERSION%.0

REM 更新AssemblyInfo.cs
powershell -Command "(Get-Content 'Properties\AssemblyInfo.cs') -replace 'AssemblyVersion\(\".*\"\)', 'AssemblyVersion(\"%NEW_ASSEMBLY_VERSION%\")' -replace 'AssemblyFileVersion\(\".*\"\)', 'AssemblyFileVersion(\"%NEW_ASSEMBLY_VERSION%\")' | Set-Content 'Properties\AssemblyInfo.cs'"

echo 版本号已递增到: %VERSION%
call :log "版本号已递增到: %VERSION%"
goto :eof

:log
echo [%date% %time%] %~1 >> "%LOG_FILE%"
goto :eof

:success
echo [√] %~1
call :log "SUCCESS: %~1"
goto :eof

:error
echo [×] 错误: %~1
call :log "ERROR: %~1"
goto :eof

:cleanup
REM 清理临时文件
if exist "%TEMP_DIR%" rmdir /s /q "%TEMP_DIR%"
if exist "%NUPKG_FILE%" del "%NUPKG_FILE%"

:end
echo.
echo 按任意键退出...
pause >nul
