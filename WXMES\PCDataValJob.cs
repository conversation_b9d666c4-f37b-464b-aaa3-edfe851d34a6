﻿using AForge.Video.DirectShow;
using EasyWork.Honor.Service;
using EasyWork.Honor.Service.Print;
using EasyWork.Honor.Service.WXMES;
using EasyWork.Model;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.IO;
using System.IO.Ports;
using System.Linq;
using System.Net;
using System.Text;
using System.Text.RegularExpressions;
using System.Windows.Forms;

namespace EasyWork.Honor.WXMES
{
    public partial class PCDataValJob : Form
    {
        DateTime starttime, endtime;
        DataTable dtt, hwdt,celt,dt, dtweight,bom;
        string dep, id = "", ScriptText;
        SerialPort myComPort;
        private FilterInfoCollection videoDevices;
        private VideoCaptureDevice videoDevice;
        private VideoCapabilities[] videoCapabilities;
        public PCDataValJob()
        {
            InitializeComponent();
            //WindowState = FormWindowState.Maximized;
            cbapprove.SelectedIndex = 0;
            string[] str = SerialPort.GetPortNames();
            if (str != null)
            {
                //添加串口项目  
                foreach (string s in SerialPort.GetPortNames())
                {//获取有多少个COM口  
                    cbport.Items.Add(s);
                }
                if (cbport.Items.Count > 0)
                {
                    cbport.SelectedIndex = 0;
                }
            }
            DataTable dtset = PublicService.QueryUserSet("DataValComScan");
            if (dtset.Rows.Count > 0)
            {
                if (dtset.Rows[0]["version"].ToString() == "N")
                {
                    tbIMEI.ReadOnly = false;
                }
            }

            videoDevices = new FilterInfoCollection(FilterCategory.VideoInputDevice);
            if (videoDevices.Count != 0)
            {
                foreach (FilterInfo device in videoDevices)
                {
                    cmbCamera.Items.Add(device.Name);
                }

                cmbCamera.SelectedIndex = 0;
            }
            else
            {
                cmbCamera.Items.Add("没有找到摄像头");
            }
            ScriptText = PrintService.Get_ZPLScript("内购标", "ALL");
           // MessageBox.Show(cmbResolution.Text);
            try
            {
                cmbResolution.Text = "1024 x 768";
            }
            catch
            {

            }

        }

        private void DataValJob_Load(object sender, EventArgs e)
        {

        }

        private void cmbCamera_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (videoDevices.Count != 0)
            {
                videoDevice = new VideoCaptureDevice(videoDevices[cmbCamera.SelectedIndex].MonikerString);
                GetDeviceResolution(videoDevice);
            }
        }

        private void GetDeviceResolution(VideoCaptureDevice videoCaptureDevice)
        {
            cmbResolution.Items.Clear();
            videoCapabilities = videoCaptureDevice.VideoCapabilities;
            foreach (VideoCapabilities capabilty in videoCapabilities)
            {
                cmbResolution.Items.Add($"{capabilty.FrameSize.Width} x {capabilty.FrameSize.Height}");
            }
            cmbResolution.SelectedIndex = 0;
        }


        private void btnConnect_Click(object sender, EventArgs e)
        {
            if (videoDevice != null)
            {
                if ((videoCapabilities != null) && (videoCapabilities.Length != 0))
                {
                    videoDevice.VideoResolution = videoCapabilities[cmbResolution.SelectedIndex];

                    vispShoot.VideoSource = videoDevice;
                    vispShoot.Start();
                    EnableControlStatus(false);
                }
            }
        }

        private void EnableControlStatus(bool status)
        {
            cmbCamera.Enabled = status;
            cmbResolution.Enabled = status;
            btnConnect.Enabled = status;
            button2.Enabled = !status;
            btnDisconnect.Enabled = !status;
        }

        private void btnDisconnect_Click(object sender, EventArgs e)
        {
            DisConnect();
            EnableControlStatus(true);
        }

        private void DisConnect()
        {
            if (vispShoot.VideoSource != null)
            {
                vispShoot.SignalToStop();
                vispShoot.WaitForStop();
                vispShoot.VideoSource = null;
            }
        }

        private void button2_Click(object sender, EventArgs e)
        {
           string str= UploadPhone();
            if (str!="1")
            {
                MessageBox.Show(str);
                return;
            }
        }

        public string UploadPhone()
        {
            Bitmap img = vispShoot.GetCurrentVideoFrame();
            if (img == null)
            {
                return "请选打开摄像头";
            }
            string fileurl = @"D:\MES\整机数据验证\";
            if (!File.Exists(fileurl))
            {
                DirectoryInfo di = new DirectoryInfo(fileurl);
                di.Create();
            }

            //添加水印
            var myGraphic = Graphics.FromImage(img);
            var sourceString = $"PC数据验证-【{tbIMEI.Text.Trim().ToUpper()}】-{DateTime.Now:g}";
            var font = new Font("Arial", 10);
            var size = myGraphic.MeasureString(sourceString, font);
            myGraphic.DrawString(sourceString, font, System.Drawing.Brushes.Red, new PointF(img.Width - size.Width - 15, img.Height - size.Height - 15));
            myGraphic.Dispose();

            string filename = tbIMEI.Text.Trim().ToUpper() + "_" + Login.usercode + "_" + PublicService.serverTime().ToString("yyyyMMddHHmmss") + ".jpg";
            img.Save(fileurl + filename);
            string url = "http://172.20.0.10:21321/api/DataValUpload";
            WebClient web = new WebClient();
            web.Headers.Add("Content-Type", filename);
            byte[] a = web.UploadFile(url, "POST", fileurl + filename);
            string str = Encoding.UTF8.GetString(a);           

        

            return str;
            
        }



        private void btquery_Click(object sender, EventArgs e)
        {
            label5.Text = "";
            if (tbIMEI.Enabled)
            {
                if (tbIMEI.Text.Trim().Length<12)
                {
                    tbIMEI.Focus();
                    return;
                }

                dep = tbweight.Text = tbpsid.Text = tbPhoneModel.Text = tbTargetCode.Text = id = tbErr.Text = tbErr0.Text = tbean.Text = "";

                if (btnConnect.Enabled)
                {
                    MessageBox.Show("请打开摄像头");
                    return;
                }
                if (btopenport.Enabled)
                {
                    MessageBox.Show("请打开电子称");
                    return;
                }

                celt = WXMESQuery.QueryCELT(tbIMEI.Text.Trim());
                if (celt.Rows.Count==0)
                {
                    MessageBox.Show("系统无该SN的CELT数据");
                    return;
                }
                //dt = WXMESQuery.QueryBasicData_New(celt.Rows[0]["PhysicsNo"].ToString().Trim());
                dt = WXMESQuery.QueryBasicData_New(tbIMEI.Text);
                if (dt.Rows.Count == 0)
                {
                    MessageBox.Show("系统无此数据！");
                    return;
                }
                if (dt.Rows[0]["department"].ToString() != "PC翻新")
                {
                    MessageBox.Show("该数据不是PC翻新数据！");
                    return;
                }
              
               // tbIMEI.Enabled = false;
                //tbCharger.Enabled = true;
                //tbCharger.Focus();
           //     return;
            }   
      
          
            if (!new[] { "DataVal","OBA","Complete" }.Contains(dt.Rows[0]["location"].ToString()))
            {
                MessageBox.Show($"不在数据验证站位,在[{dt.Rows[0]["location"].ToString()}]!");
                return;
            }
            if (dt.Rows[0]["outtype"].ToString() != "整机")
            {
                MessageBox.Show("只有[整机]需要过数据文件岗位!");
                return;
            }
         
            //if (dt.Rows[0]["AccessoriesApprove"].ToString() != "1")
            //{
            //    MessageBox.Show("请先验证配件!");
            //    return;
            //}

            //if (dt.Rows[0]["DataValapprove"].ToString() == "1")
            //{
            //    MessageBox.Show("数据文件岗位已过！");
            //    return;
            //}                

            tbpsid.Text = dt.Rows[0]["PSID"].ToString();
            tbPhoneModel.Text = dt.Rows[0]["PhoneModel"].ToString();
            tbVeneerCode.Text = dt.Rows[0]["veneercode"].ToString();
            tbTargetCode.Text = dt.Rows[0]["TargetCode"].ToString();
            id = dt.Rows[0]["id"].ToString();
            dep = dt.Rows[0]["department"].ToString();

            if (cbapprove.Text == "FAIL")
            {
                if (tbVeneerCode.Text.Trim() != "" && tbIMEI.Text.Trim() != "" && tbErr0.Text.Trim() == "")
                {
                    tbErr0.Focus();
                    return;
                }
            }
          
            tbIMEI.Enabled = false;
            starttime = PublicService.serverTime();
            tbCharger.Enabled = true;
            tbCharger.Focus();
        }

        private void bt_save_Click(object sender, EventArgs e)
        {
            if (id == "")
            {
                MessageBox.Show("请输入正确的资料!");
                return;
            }
            if (cbapprove.Text == "PASS")
            {
                if (tbweight.Enabled||tbweight.Text.Trim()=="")//new
                {
                    MessageBox.Show("[重量]有误或不能为空!");
                    return;
                }
            }
            else
            {
                if (tbErr.Text.Trim() == "")
                {
                    MessageBox.Show("请输入正确的不良故障代码，如果已输入，请回车确定!");
                    return;
                }
            }

            //endtime = PublicService.serverTime();
            //TimeSpan ts = PublicService.timeDistance(endtime, starttime);
            //if (ts.TotalSeconds > 20)
            //{
            //    MessageBox.Show("已超时(20秒),请重新再来!");
            //    errsave("SYSERR-超时作业!");
            //    return;
            //}

            //string state = WXReceives.QuerySaveLocationNew(tbIMEI.Text.Trim(), "OQC", "");
            //if (state != "1")
            //{
            //    MessageBox.Show(state);
            //    return;
            //}

            string approve = "1";
            if (cbapprove.Text == "FAIL")
            {
                approve = "0";
            }
            string err = tbErr.Text.Trim();

            ControlProcessList list = new ControlProcessList();
            list.id = id;
            list.errnumber = err;
            list.approve = approve;
            list.username = Login.usercode;
            list.job = "DataVal";
            list.location = "Complete";
            list.intime = starttime.ToString();
            list.other = tbean.Text.Trim().ToUpper() + "," + tbCharger.Text.Trim().ToUpper() + "," +tbCable.Text.Trim().ToUpper() /*+ "," + tbweight.Text.Trim()*/;
            if (WXMESPublic.GZProcedure(list) != "1")
            {
                MessageBox.Show("保存失败，请重新再试！");
                return;
            }

            string str = UploadPhone();
            if (str != "1")
            {
                MessageBox.Show(str);
                return;
            }

        

            if (chkPrintXC.Checked)
            {
                string text = ScriptText;
                for (int j = 0; j < 5; j++)
                {
                    text = text.Replace("$EAN$", "XC" + tbean.Text);
                }
                ZPLPrint zp = new ZPLPrint();
                zp.ZPL_Print(text);
            }
             
         
            btenpty_Click(null, null);
            tbIMEI.Focus();
            label5.Text = "保存成功";
        }

        public void errsave(string err)
        {  
            ControlProcessList list = new ControlProcessList();
            list.id = id;
            list.errnumber = err;
            list.approve = "0";
            list.username = Login.usercode;
            list.job = "DataVal";
            list.location = "DataVal";
            list.intime = starttime.ToString();
            list.other = tbean.Text.Trim().ToUpper() /*+ "," + tbweight.Text.Trim().ToUpper()*/;
            WXMESPublic.GZProcedure(list);
        }  

        private void tbean_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                DataTable testdt = WXMESQuery.QueryCodeData(tbTargetCode.Text.Trim(),"整机");
                if (testdt.Rows.Count < 1)
                {
                    MessageBox.Show("系统无此编码数据!");
                    errsave("SYSERR-系统无此编码数据!");
                    return;
                }
                if (testdt.Rows[0]["EAN"].ToString().Length != 13)
                {
                    MessageBox.Show("此编码的EAN码无效,请联系工段维护人员查询!");
                    errsave("SYSERR-编码EAN码无效!");
                    return;
                }

                if (testdt.Rows[0]["ean"].ToString().ToUpper() != tbean.Text.Trim().ToUpper())
                {
                    MessageBox.Show("你输入的EAN码与系统的EAN码不符!");
                    errsave("SYSERR-EAN码不符!");
                    return;
                }
                tbean.Enabled = false;
                tbxcean.Enabled = true;
                tbxcean.Focus();
                tbxcean.SelectAll();
            }
        }

        private void tbxcean_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode==Keys.Enter)
            {
                if (tbxcean.Text.Trim().ToUpper() != "XC"+tbean.Text.Trim().ToUpper())
                {
                    MessageBox.Show("彩盒EAN码与XC-EAN码不符!");
                    errsave("SYSERR-彩盒EAN码与XC-EAN码不符!");
                    return;
                }
                tbxcean.Enabled = false;
                tbweight.Enabled = true;
                tbweight.Focus();
            }
        }


        private void btenpty_Click(object sender, EventArgs e)
        {
            tbxcean.Text= tbimei1.Text= tbCharger.Text=tbCable.Text= dep = tbweight.Text = tbpsid.Text = tbPhoneModel.Text = tbTargetCode.Text = id = tbErr.Text = tbErr0.Text = tbean.Text =  tbVeneerCode.Text = tbIMEI.Text = lbweight.Text = "";
            tbIMEI.Enabled = true;
           tbxcean.Enabled= tbVeneerCode.Enabled =tbCable.Enabled=tbCharger.Enabled= tbweight.Enabled = tbean.Enabled=tbimei1.Enabled = false;
            cbapprove.SelectedIndex = 0;
            dtweight = bom=null;
        }

        private void btopenport_Click(object sender, EventArgs e)
        {

            try
            {
                myComPort = new SerialPort(cbport.Text, 9600, Parity.None);
                myComPort.DataReceived += ReceiveData;
                myComPort.Open();
                btopenport.Text = "端口已打开";
                btopenport.Enabled = false;
            }
            catch
            {

            }
        }

        private void ReceiveData(object sender, SerialDataReceivedEventArgs e)
        {
            int n = myComPort.BytesToRead;
            byte[] buf = new byte[n];
            myComPort.Read(buf, 0, n);
            tbweight.Invoke
             (new EventHandler(delegate
               {
                   if (tbweight.Enabled)
                   {
                       tbweight.Text += Regex.Replace(Encoding.ASCII.GetString(buf), @"[^\d.\d]", "");
                   }
                   else
                   {
                       string str = Encoding.ASCII.GetString(buf);
                   }
               }));
        }

        private void tbIMEI_TextChanged(object sender, EventArgs e)
        {
            if (tbIMEI.Text.Trim().Length > 0)
            {
                string str1 = tbIMEI.Text[tbIMEI.TextLength - 1].ToString();
                byte[] array = Encoding.ASCII.GetBytes(str1);
                int asciicode = (int)(array[0]);
                if (asciicode == 13)
                {
                    btquery_Click(null, null);
                }
            }
        }

        private void tbweight_KeyPress(object sender, KeyPressEventArgs e)
        {
            //string s = "1234567890." + (char)8;
            //if (s.IndexOf(e.KeyChar.ToString()) < 0)
            //{
            //    e.Handled = true;
            //}

            string s = "\r" + (char)8;
            string n = "01234567890." + (char)8;

            if (n.IndexOf(e.KeyChar.ToString()) >= 0)
            {
                e.Handled = true;
            }
        }


        private void tbweight_TextChanged(object sender, EventArgs e)
        {
            if (tbweight.Text.Trim().Length > 0)
            {
                if (tbweight.Text.Trim().Length >= 5)
                {
                    if (tbweight.Text.Trim() == "")
                    {
                        tbweight.Focus();
                        return;
                    }

                    DataTable dtweight = WXMESQuery.QueryCodeData(tbTargetCode.Text.Trim(), "整机");
                    if (dtweight.Rows.Count < 1)
                    {
                        MessageBox.Show("系统没有该编码的重量数据！");
                        errsave("SYSERR-系统没有该编码的重量数据!");
                        return;
                    }

                    string weight = dtweight.Rows[0]["weight"].ToString();
                    string errw = dtweight.Rows[0]["error"].ToString();
                    lbweight.Text = string.Format("标准重量：{0},误差:±{1}", weight, errw);
                    if (Convert.ToDouble(weight) + Convert.ToDouble(errw) < Convert.ToDouble(tbweight.Text.Trim()) || Convert.ToDouble(weight) - Convert.ToDouble(errw) > Convert.ToDouble(tbweight.Text.Trim()))
                    {
                        MessageBox.Show("重量有误！");
                        errsave("SYSERR-重量有误!");
                        return;
                    }

                    string str = UploadPhone();
                    if (str != "1")
                    {
                        MessageBox.Show(str);
                        tbweight.Text = str;
                        return;
                    }
                    tbweight.Enabled = false;
                    bt_save_Click(null, null);
                }
            }
        }







        private void tbCharger_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode==Keys.Enter)
            {
                if (tbCharger.Text.Trim().Length<10)
                {
                    tbCharger.Focus();
                    tbCharger.SelectAll();
                    return;
                }
                string ss = tbCharger.Text.Trim().Substring(0,2);
                if (ss == "69")
                {
                    MessageBox.Show("不能输入69码！");
                    tbCharger.SelectAll();
                    return;
                }
                if (int.TryParse(ss,out int _int))
                {
                    MessageBox.Show("充电头前两位不能是数值！");
                    tbCharger.SelectAll();
                    return;
                }

                dtweight = WXMESQuery.QueryCodeData(tbTargetCode.Text.Trim(), "整机");
                if (dtweight.Rows.Count < 1)
                {
                    MessageBox.Show("系统没有该编码数据！");
                    errsave("SYSERR-系统没有该编码数据!");
                    tbCharger.SelectAll();
                    return;
                }
                if (tbCharger.Text.Trim().Length<8)
                {
                    MessageBox.Show("充电头的编码有误");
                    errsave("SYSERR-充电头的编码有误");
                    return;
                }
                bom = WXMESQuery.QueryWXBOM(tbTargetCode.Text.Trim(),"配件");
                if (bom.Rows.Count==0)
                {
                    MessageBox.Show("系统没有该BOM数据");
                    errsave("SYSERR-系统没有该BOM数据");
                    return;
                }
                string code = "0222" + PublicService.DateSwitch(tbCharger.Text.Trim().Substring(1,1))+ tbCharger.Text.Trim().Substring(2, 2);

               // if (bom.Select("material='" + code + "' or material='" + code1 + "' or material='" + code2 + "' or material='" + code3 + "'").Length == 0)
                if (bom.Select("material='" + code + "'").Length == 0)
                {
                    MessageBox.Show("BOM里的充电头编码不符");
                    errsave("SYSERR-BOM里的充电头编码不符");
                    return;
                }
             
                tbCharger.Enabled = false;
                tbCable.Enabled = true;
                tbCable.Focus();
            }
        }

    

        private void tbCable_KeyDown(object sender, KeyEventArgs e)
        {

            if (e.KeyCode==Keys.Enter)
            {
                if (tbCable.Text.Trim().Length < 8)
                {
                    MessageBox.Show("数据线的编码有误");
                    errsave("SYSERR-数据线的编码有误");
                    return;
                }

                if (bom.Select("material='" + tbCable.Text.Trim().ToUpper().Substring(0, 8) + "'").Length == 0)
                {
                    MessageBox.Show("BOM里的数据线编码不符");
                    errsave("SYSERR-BOM里的数据线编码不符");
                    return;
                }
                tbCable.Enabled = false;
                tbimei1.Enabled = true;
                tbimei1.Focus();
            }

        }

        private void tbimei1_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                if (tbimei1.Text.Trim().ToUpper()!=tbIMEI.Text.Trim().ToUpper())
                {
                    MessageBox.Show("背贴SN与盒标不符");
                    errsave("SYSERR-背贴SN与盒标不符");
                    return;
                }
                tbimei1.Enabled = false;        
                tbean.Enabled = true;
                tbean.Focus();                
            }
        }


        private void button1_Click(object sender, EventArgs e)
        {
            tbIMEI.Text = "";
        }

        private void DataValJob_FormClosing(object sender, FormClosingEventArgs e)
        {
            DisConnect();
        }


        private void cbapprove_SelectedValueChanged(object sender, EventArgs e)
        {
            if (cbapprove.Text == "PASS")
            {
                tbErr.Text = tbErr0.Text = "";
                tbErr0.Enabled = false;
            }
            else
            {
                tbErr0.Enabled = true;
            }
        }



        private void tbVeneerCode_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                tbweight.Enabled = true;
                tbweight.Focus();
                btquery_Click(null, null);
            }
        }

        private void tbIMEI_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                btquery_Click(null, null);
            }
        }

        private void tbErr0_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                if (tbErr0.Text.Trim().Length != 4 && tbErr0.Text.Trim().Length != 5)
                {
                    MessageBox.Show("错误代码只能够是四或五位数！");
                    return;
                }
                DataTable dt = WXMESQuery.QueryFaultCode(tbErr0.Text.Trim());
                if (dt.Rows.Count < 1)
                {
                    MessageBox.Show("系统无此错误代码！");
                    return;
                }
                tbErr.Text = dt.Rows[0]["code"].ToString() + "-" + dt.Rows[0]["des"].ToString();
            }
        }
    }
}
