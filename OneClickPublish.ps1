# ========================================
# EasyWork.Honor One-Click Publish Script (PowerShell)
# ========================================

param(
    [string]$Version = "",
    [switch]$IncrementVersion = $false,
    [switch]$SkipBuild = $false,
    [switch]$Verbose = $false
)

# Configuration
$Config = @{
    ProjectName = "EasyWork.Honor"
    ProjectFile = "EasyWork.Honor.csproj"
    NuSpecFile = "EasyWork.Honor.nuspec"
    BuildConfig = "Release"
    Platform = "Any CPU"
    NasPath = "\\***********\public\00A-IT信息化\小狗呀\install"
    NasUser = "read"
    NasPassword = "123456"
}

# Global variables
$WorkDir = $PSScriptRoot
$BuildDir = Join-Path $WorkDir "bin\$($Config.BuildConfig)"
$TempDir = Join-Path $WorkDir "temp_publish"
$LogFile = Join-Path $WorkDir "publish_log_$(Get-Date -Format 'yyyyMMdd_HHmmss').txt"

# Log functions
function Write-Log {
    param([string]$Message, [string]$Level = "INFO")
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logEntry = "[$timestamp] [$Level] $Message"
    Write-Host $logEntry
    Add-Content -Path $LogFile -Value $logEntry
}

function Write-Success {
    param([string]$Message)
    Write-Host "[√] $Message" -ForegroundColor Green
    Write-Log $Message "SUCCESS"
}

function Write-Error {
    param([string]$Message)
    Write-Host "[×] 错误: $Message" -ForegroundColor Red
    Write-Log $Message "ERROR"
}

function Write-Warning {
    param([string]$Message)
    Write-Host "[!] 警告: $Message" -ForegroundColor Yellow
    Write-Log $Message "WARNING"
}

function Write-Step {
    param([string]$StepNumber, [string]$Description)
    Write-Host "`n[$StepNumber] $Description..." -ForegroundColor Cyan
    Write-Log "$StepNumber $Description" "STEP"
}

# 环境检查函数
function Test-Environment {
    Write-Step "1/9" "环境检查"
    
    # 检查必要工具
    $tools = @{
        "msbuild" = "MSBuild (Visual Studio Build Tools)"
        "nuget" = "NuGet CLI"
        "Squirrel" = "Squirrel.Windows"
    }
    
    foreach ($tool in $tools.Keys) {
        if (-not (Get-Command $tool -ErrorAction SilentlyContinue)) {
            Write-Error "$($tools[$tool]) 未找到，请安装后重试"
            return $false
        }
    }
    
    # 检查项目文件
    if (-not (Test-Path $Config.ProjectFile)) {
        Write-Error "项目文件 $($Config.ProjectFile) 未找到"
        return $false
    }
    
    if (-not (Test-Path $Config.NuSpecFile)) {
        Write-Error "NuSpec文件 $($Config.NuSpecFile) 未找到"
        return $false
    }
    
    Write-Success "环境检查完成"
    return $true
}

# 版本号处理函数
function Get-CurrentVersion {
    $assemblyInfoPath = "Properties\AssemblyInfo.cs"
    if (-not (Test-Path $assemblyInfoPath)) {
        Write-Error "AssemblyInfo.cs 文件未找到"
        return $null
    }
    
    $content = Get-Content $assemblyInfoPath
    $versionLine = $content | Where-Object { $_ -match 'AssemblyVersion\("(.+)"\)' }
    
    if ($versionLine) {
        $fullVersion = $Matches[1]
        # 提取前三位版本号 (1.0.0.0 -> 1.0.0)
        $versionParts = $fullVersion.Split('.')
        return "$($versionParts[0]).$($versionParts[1]).$($versionParts[2])"
    }
    
    return $null
}

function Set-Version {
    param([string]$NewVersion)
    
    $assemblyInfoPath = "Properties\AssemblyInfo.cs"
    $fullVersion = "$NewVersion.0"
    
    $content = Get-Content $assemblyInfoPath
    $content = $content -replace 'AssemblyVersion\(".*"\)', "AssemblyVersion(`"$fullVersion`")"
    $content = $content -replace 'AssemblyFileVersion\(".*"\)', "AssemblyFileVersion(`"$fullVersion`")"
    
    Set-Content -Path $assemblyInfoPath -Value $content
    Write-Log "版本号已更新为: $NewVersion"
}

function Update-NuSpecVersion {
    param([string]$NewVersion)
    
    $content = Get-Content $Config.NuSpecFile
    $content = $content -replace '<version>.*</version>', "<version>$NewVersion</version>"
    Set-Content -Path $Config.NuSpecFile -Value $content
    Write-Log "NuSpec版本号已更新为: $NewVersion"
}

# 版本号管理
function Manage-Version {
    Write-Step "2/9" "版本号处理"
    
    $currentVersion = Get-CurrentVersion
    if (-not $currentVersion) {
        Write-Error "无法获取当前版本号"
        return $null
    }
    
    Write-Log "当前版本: $currentVersion"
    
    if ($Version) {
        $targetVersion = $Version
        Write-Log "使用指定版本: $targetVersion"
    } elseif ($IncrementVersion) {
        $versionParts = $currentVersion.Split('.')
        $patch = [int]$versionParts[2] + 1
        $targetVersion = "$($versionParts[0]).$($versionParts[1]).$patch"
        Write-Log "自动递增版本: $targetVersion"
    } else {
        $response = Read-Host "当前版本: $currentVersion`n是否递增版本号? (y/n, 默认n)"
        if ($response -eq 'y' -or $response -eq 'Y') {
            $versionParts = $currentVersion.Split('.')
            $patch = [int]$versionParts[2] + 1
            $targetVersion = "$($versionParts[0]).$($versionParts[1]).$patch"
        } else {
            $targetVersion = $currentVersion
        }
    }
    
    if ($targetVersion -ne $currentVersion) {
        Set-Version $targetVersion
    }
    
    Update-NuSpecVersion $targetVersion
    Write-Success "版本号: $targetVersion"
    return $targetVersion
}

# 清理函数
function Clear-OldFiles {
    Write-Step "3/9" "清理旧文件"
    
    if (Test-Path $TempDir) {
        Remove-Item $TempDir -Recurse -Force
    }
    New-Item -ItemType Directory -Path $TempDir -Force | Out-Null
    
    # 清理旧的nupkg文件
    Get-ChildItem -Path $WorkDir -Filter "$($Config.ProjectName).*.nupkg" | Remove-Item -Force
    
    Write-Success "清理完成"
}

# 编译项目
function Build-Project {
    if ($SkipBuild) {
        Write-Step "4/9" "跳过编译 (使用现有构建)"
        return $true
    }
    
    Write-Step "4/9" "编译项目"
    
    $buildArgs = @(
        $Config.ProjectFile
        "/p:Configuration=$($Config.BuildConfig)"
        "/p:Platform=$($Config.Platform)"
        "/t:Clean,Build"
        "/v:minimal"
    )
    
    if ($Verbose) {
        $buildArgs[-1] = "/v:normal"
    }
    
    $result = & msbuild @buildArgs
    if ($LASTEXITCODE -ne 0) {
        Write-Error "项目编译失败"
        return $false
    }
    
    Write-Success "编译完成"
    return $true
}

# 生成NuGet包
function Create-NuGetPackage {
    param([string]$Version)
    
    Write-Step "5/9" "生成NuGet包"
    
    $result = & nuget pack $Config.NuSpecFile
    if ($LASTEXITCODE -ne 0) {
        Write-Error "NuGet包生成失败"
        return $null
    }
    
    $nupkgFile = "$($Config.ProjectName).$Version.nupkg"
    if (-not (Test-Path $nupkgFile)) {
        Write-Error "NuGet包文件 $nupkgFile 未找到"
        return $null
    }
    
    Write-Success "NuGet包生成完成: $nupkgFile"
    return $nupkgFile
}

# Squirrel打包
function Create-SquirrelPackage {
    param([string]$NupkgFile)
    
    Write-Step "6/9" "Squirrel打包"
    
    $result = & Squirrel --releasify $NupkgFile --releaseDir $TempDir
    if ($LASTEXITCODE -ne 0) {
        Write-Error "Squirrel打包失败"
        return $false
    }
    
    Write-Success "Squirrel打包完成"
    return $true
}

# 验证RELEASES文件
function Test-ReleasesFile {
    Write-Step "7/9" "验证RELEASES文件"
    
    $releasesFile = Join-Path $TempDir "RELEASES"
    if (-not (Test-Path $releasesFile)) {
        Write-Error "RELEASES文件未找到"
        return $false
    }
    
    $content = Get-Content $releasesFile -Raw
    if ([string]::IsNullOrWhiteSpace($content)) {
        Write-Error "RELEASES文件为空"
        return $false
    }
    
    $lines = $content.Split("`n") | Where-Object { $_.Trim() -ne "" }
    foreach ($line in $lines) {
        $parts = $line.Trim().Split(' ')
        if ($parts.Length -lt 3) {
            Write-Error "RELEASES文件格式错误: $line"
            return $false
        }
        
        $sha1 = $parts[0]
        if ($sha1.Length -ne 40 -or $sha1 -notmatch '^[a-fA-F0-9]+$') {
            Write-Error "SHA1格式错误: $sha1"
            return $false
        }
    }
    
    Write-Success "RELEASES文件验证通过"
    return $true
}

# 连接NAS
function Connect-NAS {
    Write-Step "8/9" "连接NAS服务器"
    
    # 断开现有连接
    & net use $Config.NasPath /delete 2>$null
    
    # 建立新连接
    $result = & net use $Config.NasPath $Config.NasPassword /user:$Config.NasUser
    if ($LASTEXITCODE -ne 0) {
        Write-Error "连接NAS服务器失败"
        return $false
    }
    
    Write-Success "NAS服务器连接成功"
    return $true
}

# 部署到NAS
function Deploy-ToNAS {
    param([string]$Version)
    
    Write-Step "9/9" "部署到NAS"
    
    # 备份现有RELEASES文件
    $releasesPath = Join-Path $Config.NasPath "RELEASES"
    if (Test-Path $releasesPath) {
        $backupName = "RELEASES.backup.$(Get-Date -Format 'yyyyMMdd_HHmmss')"
        $backupPath = Join-Path $Config.NasPath $backupName
        Copy-Item $releasesPath $backupPath
        Write-Log "已备份现有RELEASES文件为: $backupName"
    }
    
    # 复制新文件
    try {
        Copy-Item "$TempDir\*" $Config.NasPath -Recurse -Force
        Write-Success "部署完成"
        return $true
    } catch {
        Write-Error "部署到NAS失败: $($_.Exception.Message)"
        return $false
    }
}

# 清理临时文件
function Cleanup {
    if (Test-Path $TempDir) {
        Remove-Item $TempDir -Recurse -Force
    }
    
    # 清理nupkg文件
    Get-ChildItem -Path $WorkDir -Filter "$($Config.ProjectName).*.nupkg" | Remove-Item -Force
}

# 主函数
function Main {
    Write-Host @"

╔══════════════════════════════════════════════════════════════╗
║                    EasyWork.Honor 一键发布工具                ║
║                        PowerShell版 1.0.0                   ║
╚══════════════════════════════════════════════════════════════╝

"@ -ForegroundColor Cyan

    Write-Log "开始一键发布流程"
    
    try {
        # 1. 环境检查
        if (-not (Test-Environment)) { return }
        
        # 2. 版本号处理
        $targetVersion = Manage-Version
        if (-not $targetVersion) { return }
        
        # 3. 清理旧文件
        Clear-OldFiles
        
        # 4. 编译项目
        if (-not (Build-Project)) { return }
        
        # 5. 生成NuGet包
        $nupkgFile = Create-NuGetPackage $targetVersion
        if (-not $nupkgFile) { return }
        
        # 6. Squirrel打包
        if (-not (Create-SquirrelPackage $nupkgFile)) { return }
        
        # 7. 验证RELEASES文件
        if (-not (Test-ReleasesFile)) { return }
        
        # 8. 连接NAS
        if (-not (Connect-NAS)) { return }
        
        # 9. 部署到NAS
        if (-not (Deploy-ToNAS $targetVersion)) { return }
        
        # 成功完成
        Write-Host @"

╔══════════════════════════════════════════════════════════════╗
║                        发布成功！                           ║
║                                                              ║
║  版本: $targetVersion                                        ║
║  部署位置: $($Config.NasPath)                    ║
║  日志文件: $LogFile  ║
╚══════════════════════════════════════════════════════════════╝

"@ -ForegroundColor Green
        
        Write-Log "发布流程完成"
        
    } catch {
        Write-Error "发布过程中发生异常: $($_.Exception.Message)"
        Write-Log "EXCEPTION: $($_.Exception.Message)" "ERROR"
    } finally {
        Cleanup
    }
}

# 执行主函数
Main
