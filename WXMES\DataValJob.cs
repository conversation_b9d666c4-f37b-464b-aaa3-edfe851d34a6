﻿using AForge.Video.DirectShow;
using EasyWork.Honor.Service;
using EasyWork.Honor.Service.Print;
using EasyWork.Honor.Service.WXMES;
using EasyWork.Model;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.IO;
using System.IO.Ports;
using System.Linq;
using System.Net;
using System.Text;
using System.Text.RegularExpressions;
using System.Windows.Forms;

namespace EasyWork.Honor.WXMES
{
    public partial class DataValJob : Form
    {
        DateTime starttime, endtime;
        DataTable dtt, hwdt, celt, dt, dtweight, bom;
        string dep, id = "", ScriptText;
        SerialPort myComPort;
        private FilterInfoCollection videoDevices;
        private VideoCaptureDevice videoDevice;
        private VideoCapabilities[] videoCapabilities;
        public DataValJob()
        {
            InitializeComponent();
            //WindowState = FormWindowState.Maximized;
            cbapprove.SelectedIndex = 0;
            string[] str = SerialPort.GetPortNames();
            if (str != null)
            {
                //添加串口项目  
                foreach (string s in SerialPort.GetPortNames())
                {//获取有多少个COM口  
                    cbport.Items.Add(s);
                }
                if (cbport.Items.Count > 0)
                {
                    cbport.SelectedIndex = 0;
                }
            }
            DataTable dtset = PublicService.QueryUserSet("DataValComScan");
            if (dtset.Rows.Count > 0)
            {
                if (dtset.Rows[0]["version"].ToString() == "N")
                {
                    tbIMEI.ReadOnly = false;
                }
            }

            videoDevices = new FilterInfoCollection(FilterCategory.VideoInputDevice);
            if (videoDevices.Count != 0)
            {
                foreach (FilterInfo device in videoDevices)
                {
                    cmbCamera.Items.Add(device.Name);
                }

                cmbCamera.SelectedIndex = 0;
            }
            else
            {
                cmbCamera.Items.Add("没有找到摄像头");
            }
            ScriptText = PrintService.Get_ZPLScript("内购标", "ALL");
            // MessageBox.Show(cmbResolution.Text);
            try
            {
                cmbResolution.Text = "1024 x 768";
            }
            catch
            {

            }
        }

        private void DataValJob_Load(object sender, EventArgs e)
        {

        }

        private void cmbCamera_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (videoDevices.Count != 0)
            {
                videoDevice = new VideoCaptureDevice(videoDevices[cmbCamera.SelectedIndex].MonikerString);
                GetDeviceResolution(videoDevice);
            }
        }

        private void GetDeviceResolution(VideoCaptureDevice videoCaptureDevice)
        {
            cmbResolution.Items.Clear();
            videoCapabilities = videoCaptureDevice.VideoCapabilities;
            foreach (VideoCapabilities capabilty in videoCapabilities)
            {
                cmbResolution.Items.Add($"{capabilty.FrameSize.Width} x {capabilty.FrameSize.Height}");
            }
            cmbResolution.SelectedIndex = 0;
        }


        private void btnConnect_Click(object sender, EventArgs e)
        {
            if (videoDevice != null)
            {
                if ((videoCapabilities != null) && (videoCapabilities.Length != 0))
                {
                    videoDevice.VideoResolution = videoCapabilities[cmbResolution.SelectedIndex];

                    vispShoot.VideoSource = videoDevice;
                    vispShoot.Start();
                    EnableControlStatus(false);
                }
            }
        }

        private void EnableControlStatus(bool status)
        {
            cmbCamera.Enabled = status;
            cmbResolution.Enabled = status;
            btnConnect.Enabled = status;
            button2.Enabled = !status;
            btnDisconnect.Enabled = !status;
        }

        private void btnDisconnect_Click(object sender, EventArgs e)
        {
            DisConnect();
            EnableControlStatus(true);
        }

        private void DisConnect()
        {
            if (vispShoot.VideoSource != null)
            {
                vispShoot.SignalToStop();
                vispShoot.WaitForStop();
                vispShoot.VideoSource = null;
            }
        }

        private void button2_Click(object sender, EventArgs e)
        {
            string str = UploadPhone();
            if (str != "1")
            {
                MessageBox.Show(str);
                return;
            }
        }

        public string UploadPhone()
        {
            Bitmap img = vispShoot.GetCurrentVideoFrame();
            if (img == null)
            {
                return "请选打开摄像头";
            }
            string fileurl = @"D:\MES\整机数据验证\";
            if (!File.Exists(fileurl))
            {
                DirectoryInfo di = new DirectoryInfo(fileurl);
                di.Create();
            }

            //添加水印
            var myGraphic = Graphics.FromImage(img);
            var sourceString = $"数据验证-【{tbIMEI.Text.Trim().ToUpper()}】-{DateTime.Now:g}";
            var font = new Font("Arial", 10);
            var size = myGraphic.MeasureString(sourceString, font);
            myGraphic.DrawString(sourceString, font, System.Drawing.Brushes.Red, new PointF(img.Width - size.Width - 15, img.Height - size.Height - 15));
            myGraphic.Dispose();

            string filename = tbIMEI.Text.Trim().ToUpper() + "_" + Login.usercode + "_" + PublicService.serverTime().ToString("yyyyMMddHHmmss") + ".jpg";
            img.Save(fileurl + filename);
            string url = "http://172.20.0.10:21321/api/DataValUpload";
            WebClient web = new WebClient();
            web.Headers.Add("Content-Type", filename);
            byte[] a = web.UploadFile(url, "POST", fileurl + filename);
            string str = Encoding.UTF8.GetString(a);

            return str;

        }



        private void btquery_Click(object sender, EventArgs e)
        {
            label5.Text = "";
            if (tbIMEI.Enabled)
            {
                if (tbIMEI.Text.Trim().Length < 12)
                {
                    tbIMEI.Focus();
                    return;
                }

                dep = tbweight.Text = tbpsid.Text = tbPhoneModel.Text = tbTargetCode.Text = id = tbErr.Text = tbErr0.Text = tbimei1.Text = "";

                if (btnConnect.Enabled)
                {
                    MessageBox.Show("请打开摄像头");
                    return;
                }
                if (btopenport.Enabled)
                {
                    MessageBox.Show("请打开电子称");
                    return;
                }

                //celt = WXMESQuery.QueryCELT(tbIMEI.Text.Trim());               
                //if (celt.Rows.Count==0)
                //{
                //    MessageBox.Show("系统无该SN的CELT数据");
                //    return;
                //}
                // dt = WXMESQuery.QueryBasicData_New(celt.Rows[0]["PhysicsNo"].ToString().Trim());
                dt = WXMESQuery.QueryBasicData_New(tbIMEI.Text);
                if (dt.Rows.Count == 0)
                {
                    MessageBox.Show("系统无此数据！");
                    return;
                }

                // tbIMEI.Enabled = false;
                //tbCharger.Enabled = true;
                //tbCharger.Focus();
                //     return;
            }


            if (!new[] { "DataVal", "OBA", "Complete" }.Contains(dt.Rows[0]["location"].ToString()))
            {
                MessageBox.Show($"不在数据验证站位,在[{dt.Rows[0]["location"].ToString()}]!");
                return;
            }
            if (dt.Rows[0]["outtype"].ToString() != "整机" && dt.Rows[0]["outtype"].ToString() != "主机")
            {
                MessageBox.Show("只有[整机][主机]需要过数据文件岗位!");
                return;
            }

            //if (dt.Rows[0]["AccessoriesApprove"].ToString() != "1")
            //{
            //    MessageBox.Show("请先验证配件!");
            //    return;
            //}

            //if (dt.Rows[0]["DataValapprove"].ToString() == "1")
            //{
            //    MessageBox.Show("数据文件岗位已过！");
            //    return;
            //}                

            tbpsid.Text = dt.Rows[0]["PSID"].ToString();
            tbPhoneModel.Text = dt.Rows[0]["PhoneModel"].ToString();
            tbVeneerCode.Text = dt.Rows[0]["veneercode"].ToString();
            tbTargetCode.Text = dt.Rows[0]["TargetCode"].ToString();
            id = dt.Rows[0]["id"].ToString();
            dep = dt.Rows[0]["department"].ToString();

            if (cbapprove.Text == "FAIL")
            {
                if (tbVeneerCode.Text.Trim() != "" && tbIMEI.Text.Trim() != "" && tbErr0.Text.Trim() == "")
                {
                    tbErr0.Focus();
                    return;
                }
            }

            tbIMEI.Enabled = false;
            starttime = PublicService.serverTime();

            //部分机型不做配件验证，但称重校验环节要保留 2025.04 PQE李悦需求
            dtweight = WXMESQuery.QueryCodeData(tbTargetCode.Text.Trim(), "整机");
            if (dtweight?.Rows.Count < 1)
            {
                dtweight = WXMESQuery.QueryCodeData(tbTargetCode.Text.Trim(), "主机");
                if (dtweight?.Rows.Count < 1)
                {
                    MessageBox.Show($"系统暂无目标编码({tbTargetCode})的重量数据！");
                    return;
                }
            }

            //获取需要验证的首个配件文本框
            //var firstVerifyTextBox = GetFristVerifyTextBox();
            //if (firstVerifyTextBox != null)
            //{
            //    firstVerifyTextBox.Enabled = true;
            //    firstVerifyTextBox.Focus();
            //}
            //else
            //{
            //    //没有任何配件需要验证，直接到彩盒标
            //    tbimei1.Enabled = true;
            //    tbimei1.Focus();
            //}

            tbCharger.Enabled = true;
            tbCharger.Focus();
        }

        private void bt_save_Click(object sender, EventArgs e)
        {
            if (id == "")
            {
                MessageBox.Show("请输入正确的资料!");
                return;
            }
            if (cbapprove.Text == "PASS")
            {
                if (tbweight.Enabled || tbweight.Text.Trim() == "")//new
                {
                    MessageBox.Show("[重量]有误或不能为空!");
                    return;
                }
            }
            else
            {
                if (tbErr.Text.Trim() == "")
                {
                    MessageBox.Show("请输入正确的不良故障代码，如果已输入，请回车确定!");
                    return;
                }
            }

            endtime = PublicService.serverTime();
            TimeSpan ts = PublicService.timeDistance(endtime, starttime);
            if (ts.TotalSeconds > 20)
            {
                //MessageBox.Show("已超时(20秒),请重新再来!");
                //errsave("SYSERR-超时作业!");
                //return;
            }

            //string state = WXReceives.QuerySaveLocationNew(tbIMEI.Text.Trim(), "OQC", "");
            //if (state != "1")
            //{
            //    MessageBox.Show(state);
            //    return;
            //}

            string approve = "1";
            if (cbapprove.Text == "FAIL")
            {
                approve = "0";
            }
            string err = tbErr.Text.Trim();

            ControlProcessList list = new ControlProcessList();
            list.id = id;
            list.errnumber = err;
            list.approve = approve;
            list.username = Login.usercode;
            list.job = "DataVal";
            list.location = "OBA";
            list.intime = starttime.ToString();
            list.other = tbimei1.Text.Trim().ToUpper() + "," + tbCharger.Text.Trim().ToUpper() + "," + tbCable.Text.Trim().ToUpper() + "," + tbHeadset.Text.Trim().ToUpper() + "," + tbPen.Text.Trim().ToUpper() + "," + tbNib.Text.Trim().ToUpper() + "," + tbKeypanel.Text.Trim().ToUpper() + "," + tbweight.Text.Trim();
            if (WXMESPublic.GZProcedure(list) != "1")
            {
                MessageBox.Show("保存失败，请重新再试！");
                return;
            }

            string str = UploadPhone();
            if (str != "1")
            {
                MessageBox.Show(str);
                return;
            }



            if (chkPrintXC.Checked)
            {
                string text = ScriptText;
                for (int j = 0; j < 5; j++)
                {
                    text = text.Replace("$EAN$", "XC" + tbimei1.Text);
                }
                ZPLPrint zp = new ZPLPrint();
                zp.ZPL_Print(text);
            }


            btenpty_Click(null, null);
            tbIMEI.Focus();
            label5.Text = "保存成功";
        }

        public void errsave(string err)
        {
            ControlProcessList list = new ControlProcessList();
            list.id = id;
            list.errnumber = err;
            list.approve = "0";
            list.username = Login.usercode;
            list.job = "DataVal";
            list.location = "DataVal";
            list.intime = starttime.ToString();
            list.other = tbimei1.Text.Trim().ToUpper() + "," + tbweight.Text.Trim().ToUpper();
            WXMESPublic.GZProcedure(list);
        }

        private void tbean_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                DataTable testdt = WXMESQuery.QueryCodeData(tbTargetCode.Text.Trim(), "整机");
                if (testdt.Rows.Count < 1)
                {
                    testdt = WXMESQuery.QueryCodeData(tbTargetCode.Text.Trim(), "主机");
                    if (testdt.Rows.Count < 1)
                    {

                        MessageBox.Show("系统无此编码数据!");
                        errsave("SYSERR-系统无此编码数据!");
                        return;
                    }
                }
                if (testdt.Rows[0]["EAN"].ToString().Length != 13)
                {
                    MessageBox.Show("此编码的EAN码无效,请联系工段维护人员查询!");
                    errsave("SYSERR-编码EAN码无效!");
                    return;
                }

                if (testdt.Rows[0]["ean"].ToString().ToUpper() != tbimei1.Text.Trim().ToUpper())
                {
                    MessageBox.Show("你输入的EAN码与系统的EAN码不符!");
                    errsave("SYSERR-EAN码不符!");
                    return;
                }
                tbimei1.Enabled = false;
                tbxcean.Enabled = true;
                tbxcean.Focus();
                tbxcean.SelectAll();
            }
        }

        private void tbxcean_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                if (tbxcean.Text.Trim().ToUpper() != "XC" + tbimei1.Text.Trim().ToUpper())
                {
                    MessageBox.Show("彩盒EAN码与XC-EAN码不符!");
                    errsave("SYSERR-彩盒EAN码与XC-EAN码不符!");
                    return;
                }
                tbxcean.Enabled = false;
                tbweight.Enabled = true;
                tbweight.Focus();
            }
        }

        private void tbweight_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                if (tbweight.Text.Trim() == "")
                {
                    tbweight.Focus();
                    return;
                }

                if (dtweight.Rows.Count < 1)
                {
                    MessageBox.Show("系统没有该编码的重量数据！");
                    errsave("SYSERR-系统没有该编码的重量数据!");
                    return;
                }

                string weight = dtweight.Rows[0]["weight"].ToString();
                string errw = dtweight.Rows[0]["error"].ToString();
                lbweight.Text = string.Format("标准重量：{0},误差:±{1}", weight, errw);
                if (Convert.ToDouble(weight) + Convert.ToDouble(errw) < Convert.ToDouble(tbweight.Text.Trim()) || Convert.ToDouble(weight) - Convert.ToDouble(errw) > Convert.ToDouble(tbweight.Text.Trim()))
                {
                    MessageBox.Show("重量有误！");
                    errsave("SYSERR-重量有误!");
                    return;
                }

                string str = UploadPhone();
                if (str != "1")
                {
                    MessageBox.Show(str);
                    tbweight.Text = str;
                    return;
                }
                tbweight.Enabled = false;
                bt_save_Click(null, null);
            }
        }

        private void btenpty_Click(object sender, EventArgs e)
        {
            //清空所有文本框
            tbxcean.Text = tbean.Text = tbCharger.Text = tbCable.Text =
            tbHeadset.Text = tbKeypanel.Text = tbPen.Text = tbNib.Text =
            dep = tbweight.Text = tbpsid.Text = tbPhoneModel.Text =
            tbTargetCode.Text = id = tbErr.Text = tbErr0.Text =
            tbimei1.Text = tbVeneerCode.Text = tbIMEI.Text = lbweight.Text = "";

            //禁用所有验证项，只保留背贴
            tbIMEI.Enabled = true;
            tbxcean.Enabled = tbVeneerCode.Enabled = tbHeadset.Enabled =
            tbKeypanel.Enabled = tbPen.Enabled = tbNib.Enabled =
            tbCable.Enabled = tbCharger.Enabled = tbweight.Enabled =
            tbimei1.Enabled = tbean.Enabled = false;

            //剩余验证项
            cbapprove.SelectedIndex = 0;
            dtweight = bom = null;
        }

        private void btopenport_Click(object sender, EventArgs e)
        {

            try
            {
                myComPort = new SerialPort(cbport.Text, 9600, Parity.None);
                myComPort.DataReceived += ReceiveData;
                myComPort.Open();
                btopenport.Text = "端口已打开";
                btopenport.Enabled = false;
            }
            catch
            {

            }
        }

        private void ReceiveData(object sender, SerialDataReceivedEventArgs e)
        {
            int n = myComPort.BytesToRead;
            byte[] buf = new byte[n];
            myComPort.Read(buf, 0, n);
            tbweight.Invoke
             (new EventHandler(delegate
             {
                 if (tbweight.Enabled)
                 {
                     tbweight.Text += Regex.Replace(Encoding.ASCII.GetString(buf), @"[^\d.\d]", "");
                 }
                 else
                 {
                     string str = Encoding.ASCII.GetString(buf);
                 }
             }));
        }

        private void tbIMEI_TextChanged(object sender, EventArgs e)
        {
            if (tbIMEI.Text.Trim().Length > 0)
            {
                string str1 = tbIMEI.Text[tbIMEI.TextLength - 1].ToString();
                byte[] array = Encoding.ASCII.GetBytes(str1);
                int asciicode = (int)(array[0]);
                if (asciicode == 13)
                {
                    btquery_Click(null, null);
                }
            }
        }

        private void tbweight_TextChanged(object sender, EventArgs e)
        {
            if (tbweight.Text.Trim().Length > 0)
            {
                if (tbweight.Text.Trim().Length >= 5)
                {
                    if (tbweight.Text.Trim() == "")
                    {
                        tbweight.Focus();
                        return;
                    }

                    DataTable dtweight = WXMESQuery.QueryCodeData(tbTargetCode.Text.Trim(), "整机");
                    if (dtweight.Rows.Count < 1)
                    {
                        dtweight = WXMESQuery.QueryCodeData(tbTargetCode.Text.Trim(), "主机");
                        if (dtweight.Rows.Count < 1)
                        {
                            MessageBox.Show("系统没有该编码的重量数据！");
                            errsave("SYSERR-系统没有该编码的重量数据!");
                            return;
                        }
                    }

                    string weight = dtweight.Rows[0]["weight"].ToString();
                    string errw = dtweight.Rows[0]["error"].ToString();
                    lbweight.Text = string.Format("标准重量：{0},误差:±{1}", weight, errw);
                    if (Convert.ToDouble(weight) + Convert.ToDouble(errw) < Convert.ToDouble(tbweight.Text.Trim()) || Convert.ToDouble(weight) - Convert.ToDouble(errw) > Convert.ToDouble(tbweight.Text.Trim()))
                    {
                        MessageBox.Show("重量有误！");
                        errsave("SYSERR-重量有误!");
                        return;
                    }

                    string str = UploadPhone();
                    if (str != "1")
                    {
                        MessageBox.Show(str);
                        tbweight.Text = str;
                        return;
                    }
                    tbweight.Enabled = false;
                    bt_save_Click(null, null);
                }
            }
        }

        private void tbweight_KeyPress(object sender, KeyPressEventArgs e)
        {
            string s = "1234567890." + (char)8;
            if (s.IndexOf(e.KeyChar.ToString()) < 0)
            {
                e.Handled = true;
            }
        }

        private void button1_Click(object sender, EventArgs e)
        {
            tbIMEI.Text = "";
        }

        private void DataValJob_FormClosing(object sender, FormClosingEventArgs e)
        {
            DisConnect();
        }

        private void cbapprove_SelectedValueChanged(object sender, EventArgs e)
        {
            if (cbapprove.Text == "PASS")
            {
                tbErr.Text = tbErr0.Text = "";
                tbErr0.Enabled = false;
            }
            else
            {
                tbErr0.Enabled = true;
            }
        }

        private void tbVeneerCode_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                btquery_Click(null, null);
            }
        }

        private void tbIMEI_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                btquery_Click(null, null);
            }
        }

        private void tbErr0_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                if (tbErr0.Text.Trim().Length != 4 && tbErr0.Text.Trim().Length != 5)
                {
                    MessageBox.Show("错误代码只能够是四或五位数！");
                    return;
                }
                DataTable dt = WXMESQuery.QueryFaultCode(tbErr0.Text.Trim());
                if (dt.Rows.Count < 1)
                {
                    MessageBox.Show("系统无此错误代码！");
                    return;
                }
                tbErr.Text = dt.Rows[0]["code"].ToString() + "-" + dt.Rows[0]["des"].ToString();
            }
        }


        #region/**** 配件验证岗位校验逻辑通用封装 wonder.chia  2025.04 ****/

        /// <summary>
        /// 通用的配件文本框按键事件处理 2025.04 PQE李悦需求
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void AnyTextBox_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode != Keys.Enter) return;

            if (!(sender is TextBox currentTextBox)) return;

            string prefix = null;
            int codeLength = 8;

            //这里根据不同的文本框设置不同的编码规则
            switch (currentTextBox.Name)
            {
                case "tbCharger": //李悦需求 充头需要特殊处理
                    if (currentTextBox.Text.Trim().Length < 10)
                    {
                        currentTextBox.SelectAll();
                        return;
                    }
                    var ss = currentTextBox.Text.Trim().Substring(0, 2);
                    if (ss == "69")
                    {
                        MessageBox.Show("不能输入69码！");
                        currentTextBox.SelectAll();
                        return;
                    }
                    if (int.TryParse(ss, out int _int))
                    {
                        MessageBox.Show("充电头前两位不能是数值！");
                        currentTextBox.SelectAll();
                        return;
                    }
                    prefix = "0222";
                    codeLength = 4;
                    break;
                case "tbHeadset":
                case "tbPen":
                case "tbNib":
                case "tbKeypanel":
                    prefix = "2204";
                    codeLength = 4;
                    break;
            }

            //查询BOM表绑定的目标编码
            if (bom == null)
            {
                bom = WXMESQuery.QueryWXBOM(tbTargetCode.Text.Trim(), "配件");
                if (bom?.Rows.Count == 0)
                {
                    MessageBox.Show("系统没有该BOM数据");
                    errsave("SYSERR-系统没有该BOM数据");
                    return;
                }
            }

            //做完了切换到下一个控件
            if (!CheckAndEnableNext(currentTextBox, prefix, codeLength))
            {
                currentTextBox.SelectAll();
            }
        }

        /// <summary>
        /// 获取下一个需要启用的文本框
        /// </summary>
        /// <param name="currentBox"></param>
        /// <returns></returns>
        private (TextBox nextBox, string columnName) GetNextValidTextBox(TextBox currentBox)
        {
            //定义配件校验顺序及其对应的数据库列名
            var textBoxSequence = new[] {
                (box: tbCharger, column: "Charger"),
                (box: tbCable, column: "DataLine"),
                (box: tbHeadset, column: "Headset"),
                (box: tbPen, column: "Pen"),
                (box: tbNib, column: "Nib"),
                (box: tbKeypanel, column: "Keypanel"),
                (box: tbimei1, column: "IMEI"),         
                (box: tbxcean, column: "XC69")          
            };

            //查找当前文本框在序列中的索引
            var currentIndex = Array.FindIndex(textBoxSequence, x => x.box == currentBox);
            if (currentIndex < 0) return (null, null);

            //遍历后续的文本框，查找下一个需要启用的文本框
            for (int i = currentIndex + 1; i < textBoxSequence.Length; i++)
            {
                var (box, column) = textBoxSequence[i];

                //三种情况：1、是IMEI且还未到XC69；2、是内购码且需要验证；3、其他普通配件且需要验证
                if ((column == "IMEI" && currentBox != tbxcean) || (column == "XC69" && dtweight?.Rows[0]["XC69"]?.ToString() == "1") || (column != "IMEI" && column != "XC69" && dtweight?.Rows[0][column]?.ToString() == "1"))
                {
                    return (box, column);
                }
            }

            //什么都没找到，到达终点
            return (null, null);
        }

        /// <summary>
        /// 检查当前文本框内容并启用下一个文本框
        /// </summary>
        /// <param name="textBox"></param>
        /// <param name="prefix"></param>
        /// <param name="codeLength"></param>
        /// <returns></returns>
        private bool CheckAndEnableNext(TextBox textBox, string prefix, int codeLength)
        {
            try
            {
                //校验当前文本框的内容合法性
                if (!ValidateTextBox(textBox)) return false;

                //获取当前文本框的内容并进行处理
                var inputCode = textBox.Text.Trim().ToUpper();

                var startIndex = textBox.Name == "tbCharger" ? 2 : 0; //充头需要从第二位开始截取

                var materialCode = prefix != null ? prefix + inputCode.Substring(startIndex, codeLength) : inputCode.Substring(0, 8);

                //在BOM表中查找物料编码
                if (bom?.Select($"material='{materialCode}'").Length == 0 && textBox.Name != "tbimei1")
                {
                    materialCode = inputCode.Substring(0, 8); //用完整8位编码去找
                    if (bom?.Select($"material='{materialCode}'").Length == 0)
                    {
                        //两次查找都失败？直接返回报错
                        MessageBox.Show($"BOM里的{textBox.Tag}编码不符");
                        errsave($"SYSERR-BOM里的{textBox.Tag}编码不符");
                        return false;
                    }
                }

                //获取下一个需要校验的文本框
                var (nextBox, _) = GetNextValidTextBox(textBox);

                if (nextBox != null)
                {
                    //还有下一个要验证的
                    textBox.Enabled = false;
                    nextBox.Enabled = true;
                    nextBox.Focus();
                }
                else
                {
                    // 验证完成，进入称重环节
                    textBox.Enabled = false;
                    tbweight.Enabled = true;
                    tbweight.Focus();
                }

                return true;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"验证{textBox.Tag}时发生错误: {ex.Message}");
                errsave($"SYSERR-验证{textBox.Tag}时发生错误");
                return false;
            }
        }

        /// <summary>
        /// 获取首个需要验证的文本框
        /// </summary>
        /// <returns></returns>
        private TextBox GetFristVerifyTextBox()
        {
            //定义可能验证的配件文本框
            var textboxs = new[] {
                (box: tbCharger, column: "Charger"),
                (box: tbCable, column: "DataLine"),
                (box: tbHeadset, column: "Headset"),
                (box: tbPen, column: "Pen"),
                (box: tbNib, column: "Nib"),
                (box: tbxcean, column: "XC69"),
                (box: tbKeypanel, column: "Keypanel")
            };
            //逐个进行检查
            foreach (var (box, column) in textboxs)
            {
                if (dtweight?.Rows.Count > 0 && dtweight.Rows[0][column]?.ToString() == "1")
                {
                    return box;
                }
            }

            return null; //不做配件验证
        }

        /// <summary>
        /// 校验编码合法性
        /// </summary>
        /// <param name="textBox"></param>
        /// <returns></returns>
        private bool ValidateTextBox(TextBox textBox)
        {
            //先判空
            if (string.IsNullOrEmpty(textBox.Text))
            {
                MessageBox.Show($"请输入{textBox.Tag}编码");
                return false;
            }

            //检查是否包含非数字字符
            if (textBox.Text.Trim().Length < 8)
            {
                MessageBox.Show($"{textBox.Tag}的编码有误");
                errsave($"SYSERR-{textBox.Tag}的编码有误");
                return false;
            }
            return true;
        }

        #endregion


    }
}
