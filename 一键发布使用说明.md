# EasyWork.Honor 一键发布工具使用说明

## 概述

现在您拥有了完整的一键发布解决方案，可以实现从代码编译到NAS部署的全自动化流程。

## 文件说明

### 核心脚本文件
- **OneClickPublish.bat** - 批处理版本（推荐Windows用户）
- **OneClickPublish.ps1** - PowerShell版本（功能更强大）
- **PublishConfig.json** - 配置文件（可选）

### 功能对比

| 功能 | 批处理版 | PowerShell版 |
|------|----------|--------------|
| 基本发布流程 | ✅ | ✅ |
| 版本号自动递增 | ✅ | ✅ |
| 详细错误处理 | ✅ | ✅ |
| 命令行参数 | ❌ | ✅ |
| 高级配置 | ❌ | ✅ |
| 彩色输出 | ❌ | ✅ |
| 异常处理 | 基础 | 完整 |

## 使用方法

### 方法1：双击运行（最简单）

1. **批处理版本**：
   ```
   双击 OneClickPublish.bat
   ```

2. **PowerShell版本**：
   ```
   右键 OneClickPublish.ps1 → "使用PowerShell运行"
   ```

### 方法2：命令行运行（PowerShell版）

```powershell
# 基本运行
.\OneClickPublish.ps1

# 自动递增版本号
.\OneClickPublish.ps1 -IncrementVersion

# 指定版本号
.\OneClickPublish.ps1 -Version "1.2.5"

# 跳过编译（使用现有构建）
.\OneClickPublish.ps1 -SkipBuild

# 详细输出
.\OneClickPublish.ps1 -Verbose

# 组合参数
.\OneClickPublish.ps1 -IncrementVersion -Verbose
```

## 发布流程详解

### 自动化步骤

1. **环境检查**
   - 验证MSBuild、NuGet、Squirrel工具
   - 检查项目文件和NuSpec文件

2. **版本号处理**
   - 读取当前版本号
   - 可选择递增或指定新版本
   - 自动更新AssemblyInfo.cs和.nuspec文件

3. **清理旧文件**
   - 清理临时目录
   - 删除旧的NuGet包

4. **编译项目**
   - 使用Release配置编译
   - 支持跳过编译选项

5. **生成NuGet包**
   - 自动打包项目
   - 验证包文件生成

6. **Squirrel打包**
   - 生成安装包和更新包
   - 创建RELEASES文件

7. **验证RELEASES文件**
   - 检查文件格式
   - 验证SHA1哈希值

8. **连接NAS服务器**
   - 自动建立网络连接
   - 处理认证

9. **部署到NAS**
   - 备份现有文件
   - 复制新版本文件

## 前置要求

### 必需工具

1. **MSBuild**
   - Visual Studio 2017+ 或
   - Build Tools for Visual Studio

2. **NuGet CLI**
   ```cmd
   # 下载并添加到PATH
   https://www.nuget.org/downloads
   ```

3. **Squirrel.Windows**
   ```cmd
   # 通过NuGet安装
   nuget install squirrel.windows -OutputDirectory tools
   # 或通过Chocolatey
   choco install squirrel
   ```

### 项目文件要求

确保项目根目录包含：
- `EasyWork.Honor.csproj`
- `EasyWork.Honor.nuspec`
- `Properties\AssemblyInfo.cs`

### NuSpec文件示例

```xml
<?xml version="1.0" encoding="utf-8"?>
<package xmlns="http://schemas.microsoft.com/packaging/2010/07/nuspec.xsd">
  <metadata>
    <id>EasyWork.Honor</id>
    <version>1.0.0</version>
    <title>EasyWork Honor</title>
    <authors>Your Company</authors>
    <description>EasyWork Honor Application</description>
    <requireLicenseAcceptance>false</requireLicenseAcceptance>
  </metadata>
  <files>
    <file src="bin\Release\**" target="lib\net48\" />
  </files>
</package>
```

## 配置自定义

### 修改脚本参数

在脚本文件中找到配置部分并修改：

**批处理版 (OneClickPublish.bat)**：
```batch
set PROJECT_NAME=EasyWork.Honor
set NAS_PATH=\\***********\public\00A-IT信息化\小狗呀\install
set NAS_USER=read
set NAS_PASS=123456
```

**PowerShell版 (OneClickPublish.ps1)**：
```powershell
$Config = @{
    ProjectName = "EasyWork.Honor"
    NasPath = "\\***********\public\00A-IT信息化\小狗呀\install"
    NasUser = "read"
    NasPassword = "123456"
}
```

## 故障排除

### 常见问题

1. **"MSBuild 未找到"**
   - 安装Visual Studio或Build Tools
   - 确保MSBuild在PATH环境变量中

2. **"NuGet CLI 未找到"**
   - 下载nuget.exe并添加到PATH
   - 或使用包管理器安装

3. **"Squirrel 未找到"**
   - 通过NuGet或Chocolatey安装Squirrel
   - 确保Squirrel.exe在PATH中

4. **"连接NAS服务器失败"**
   - 检查网络连接
   - 验证用户名和密码
   - 确认NAS路径正确

5. **"项目编译失败"**
   - 检查项目依赖
   - 确保所有NuGet包已还原
   - 验证.NET Framework版本

### 日志文件

每次运行都会生成详细的日志文件：
- 文件名格式：`publish_log_YYYYMMDD_HHMMSS.txt`
- 包含完整的执行过程和错误信息
- 用于问题诊断和调试

## 最佳实践

1. **发布前检查**
   - 确保代码已提交到版本控制
   - 运行单元测试
   - 验证功能完整性

2. **版本管理**
   - 遵循语义化版本规范
   - 记录版本变更日志
   - 标记重要版本

3. **备份策略**
   - 脚本会自动备份RELEASES文件
   - 建议定期备份整个发布目录
   - 保留关键版本的完整备份

4. **测试部署**
   - 在测试环境先验证
   - 确认自动更新功能正常
   - 验证客户端更新流程

通过这个一键发布工具，您现在可以实现真正的一键发布，大大简化了发布流程并减少了人为错误的可能性。
