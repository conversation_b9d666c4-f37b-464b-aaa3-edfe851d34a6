# Simple One-Click Publish Script
param(
    [string]$Version = "",
    [switch]$IncrementVersion = $false
)

# Configuration
$ProjectName = "EasyWork.Honor"
$ProjectFile = "$ProjectName.csproj"
$NuSpecFile = "$ProjectName.nuspec"
$BuildConfig = "Release"
$Platform = "Any CPU"
$NasPath = "\\***********\public\00A-IT信息化\小狗呀\install"
$NasUser = "read"
$NasPassword = "123456"

$WorkDir = $PSScriptRoot
$TempDir = Join-Path $WorkDir "temp_publish"
$LogFile = Join-Path $WorkDir "publish_log_$(Get-Date -Format 'yyyyMMdd_HHmmss').txt"

function Write-Log {
    param([string]$Message)
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logEntry = "[$timestamp] $Message"
    Write-Host $logEntry
    Add-Content -Path $LogFile -Value $logEntry
}

function Write-Success {
    param([string]$Message)
    Write-Host "[√] $Message" -ForegroundColor Green
    Write-Log "SUCCESS: $Message"
}

function Write-Error {
    param([string]$Message)
    Write-Host "[×] ERROR: $Message" -ForegroundColor Red
    Write-Log "ERROR: $Message"
}

Write-Host @"

===============================================================
                EasyWork.Honor Simple Publish Tool
===============================================================

"@ -ForegroundColor Cyan

Write-Log "Starting publish process"

try {
    # 1. Environment Check
    Write-Host "[1/8] Environment Check..." -ForegroundColor Cyan
    
    $tools = @("msbuild", "nuget", "Squirrel")
    foreach ($tool in $tools) {
        if (-not (Get-Command $tool -ErrorAction SilentlyContinue)) {
            Write-Error "$tool not found"
            return
        }
    }
    
    if (-not (Test-Path $ProjectFile)) {
        Write-Error "Project file $ProjectFile not found"
        return
    }
    
    if (-not (Test-Path $NuSpecFile)) {
        Write-Error "NuSpec file $NuSpecFile not found"
        return
    }
    
    Write-Success "Environment check completed"
    
    # 2. Version Processing
    Write-Host "[2/8] Version Processing..." -ForegroundColor Cyan
    
    $assemblyInfoPath = "Properties\AssemblyInfo.cs"
    $content = Get-Content $assemblyInfoPath
    $versionLine = $content | Where-Object { $_ -match 'AssemblyVersion\("(.+)"\)' }
    
    if ($versionLine) {
        $fullVersion = $Matches[1]
        $versionParts = $fullVersion.Split('.')
        $currentVersion = "$($versionParts[0]).$($versionParts[1]).$($versionParts[2])"
    } else {
        Write-Error "Cannot get current version"
        return
    }
    
    Write-Log "Current version: $currentVersion"
    
    if ($Version) {
        $targetVersion = $Version
    } elseif ($IncrementVersion) {
        $versionParts = $currentVersion.Split('.')
        $patch = [int]$versionParts[2] + 1
        $targetVersion = "$($versionParts[0]).$($versionParts[1]).$patch"
    } else {
        $response = Read-Host "Current version: $currentVersion. Increment version? (y/n, default n)"
        if ($response -eq 'y' -or $response -eq 'Y') {
            $versionParts = $currentVersion.Split('.')
            $patch = [int]$versionParts[2] + 1
            $targetVersion = "$($versionParts[0]).$($versionParts[1]).$patch"
        } else {
            $targetVersion = $currentVersion
        }
    }
    
    if ($targetVersion -ne $currentVersion) {
        $fullVersion = "$targetVersion.0"
        $content = $content -replace 'AssemblyVersion\(".*"\)', "AssemblyVersion(`"$fullVersion`")"
        $content = $content -replace 'AssemblyFileVersion\(".*"\)', "AssemblyFileVersion(`"$fullVersion`")"
        Set-Content -Path $assemblyInfoPath -Value $content
    }
    
    # Update NuSpec version
    $nuspecContent = Get-Content $NuSpecFile
    $nuspecContent = $nuspecContent -replace '<version>.*</version>', "<version>$targetVersion</version>"
    Set-Content -Path $NuSpecFile -Value $nuspecContent
    
    Write-Success "Version: $targetVersion"
    
    # 3. Clean old files
    Write-Host "[3/8] Cleaning old files..." -ForegroundColor Cyan
    
    if (Test-Path $TempDir) {
        Remove-Item $TempDir -Recurse -Force
    }
    New-Item -ItemType Directory -Path $TempDir -Force | Out-Null
    
    Get-ChildItem -Path $WorkDir -Filter "$ProjectName.*.nupkg" | Remove-Item -Force
    
    Write-Success "Cleanup completed"
    
    # 4. Build project
    Write-Host "[4/8] Building project..." -ForegroundColor Cyan
    
    & msbuild $ProjectFile /p:Configuration=$BuildConfig /p:Platform="$Platform" /t:Clean,Build /v:minimal
    if ($LASTEXITCODE -ne 0) {
        Write-Error "Project build failed"
        return
    }
    
    Write-Success "Build completed"
    
    # 5. Create NuGet package
    Write-Host "[5/8] Creating NuGet package..." -ForegroundColor Cyan
    
    & nuget pack $NuSpecFile
    if ($LASTEXITCODE -ne 0) {
        Write-Error "NuGet package creation failed"
        return
    }
    
    $nupkgFile = "$ProjectName.$targetVersion.nupkg"
    if (-not (Test-Path $nupkgFile)) {
        Write-Error "NuGet package file $nupkgFile not found"
        return
    }
    
    Write-Success "NuGet package created: $nupkgFile"
    
    # 6. Squirrel packaging
    Write-Host "[6/8] Squirrel packaging..." -ForegroundColor Cyan
    
    & Squirrel --releasify $nupkgFile --releaseDir $TempDir
    if ($LASTEXITCODE -ne 0) {
        Write-Error "Squirrel packaging failed"
        return
    }
    
    Write-Success "Squirrel packaging completed"
    
    # 7. Connect to NAS
    Write-Host "[7/8] Connecting to NAS server..." -ForegroundColor Cyan
    
    & net use $NasPath /delete 2>$null
    & net use $NasPath $NasPassword /user:$NasUser
    if ($LASTEXITCODE -ne 0) {
        Write-Error "NAS server connection failed"
        return
    }
    
    Write-Success "NAS server connected successfully"
    
    # 8. Deploy to NAS
    Write-Host "[8/8] Deploying to NAS..." -ForegroundColor Cyan
    
    # Backup existing RELEASES file
    $releasesPath = Join-Path $NasPath "RELEASES"
    if (Test-Path $releasesPath) {
        $backupName = "RELEASES.backup.$(Get-Date -Format 'yyyyMMdd_HHmmss')"
        $backupPath = Join-Path $NasPath $backupName
        Copy-Item $releasesPath $backupPath
        Write-Log "Backed up existing RELEASES file as: $backupName"
    }
    
    # Copy new files
    Copy-Item "$TempDir\*" $NasPath -Recurse -Force
    Write-Success "Deployment completed"
    
    # Success message
    Write-Host @"

===============================================================
                        PUBLISH SUCCESS!

  Version: $targetVersion
  Deploy Location: $NasPath
  Log File: $LogFile
===============================================================

"@ -ForegroundColor Green
    
    Write-Log "Publish process completed successfully"
    
} catch {
    Write-Error "Publish process failed: $($_.Exception.Message)"
    Write-Log "EXCEPTION: $($_.Exception.Message)"
} finally {
    # Cleanup
    if (Test-Path $TempDir) {
        Remove-Item $TempDir -Recurse -Force
    }
    Get-ChildItem -Path $WorkDir -Filter "$ProjectName.*.nupkg" | Remove-Item -Force
}

Write-Host "`nPress any key to exit..."
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
