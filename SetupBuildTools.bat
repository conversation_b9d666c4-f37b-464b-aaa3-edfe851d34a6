@echo off
setlocal enabledelayedexpansion

echo ===============================================================
echo                    Build Tools Setup
echo ===============================================================
echo.

echo Searching for MSBuild...

REM 常见的MSBuild路径
set "MSBUILD_PATHS="
set "MSBUILD_PATHS=!MSBUILD_PATHS!;C:\Program Files\Microsoft Visual Studio\2022\Enterprise\MSBuild\Current\Bin"
set "MSBUILD_PATHS=!MSBUILD_PATHS!;C:\Program Files\Microsoft Visual Studio\2022\Professional\MSBuild\Current\Bin"
set "MSBUILD_PATHS=!MSBUILD_PATHS!;C:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin"
set "MSBUILD_PATHS=!MSBUILD_PATHS!;C:\Program Files (x86)\Microsoft Visual Studio\2019\Enterprise\MSBuild\Current\Bin"
set "MSBUILD_PATHS=!MSBUILD_PATHS!;C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\MSBuild\Current\Bin"
set "MSBUILD_PATHS=!MSBUILD_PATHS!;C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\MSBuild\Current\Bin"
set "MSBUILD_PATHS=!MSBUILD_PATHS!;C:\Program Files (x86)\Microsoft Visual Studio\2017\Enterprise\MSBuild\15.0\Bin"
set "MSBUILD_PATHS=!MSBUILD_PATHS!;C:\Program Files (x86)\Microsoft Visual Studio\2017\Professional\MSBuild\15.0\Bin"
set "MSBUILD_PATHS=!MSBUILD_PATHS!;C:\Program Files (x86)\Microsoft Visual Studio\2017\Community\MSBuild\15.0\Bin"
set "MSBUILD_PATHS=!MSBUILD_PATHS!;C:\Program Files (x86)\MSBuild\14.0\Bin"

set "FOUND_MSBUILD="

for %%p in (%MSBUILD_PATHS:;= %) do (
    if exist "%%p\MSBuild.exe" (
        echo Found MSBuild at: %%p
        set "FOUND_MSBUILD=%%p"
        goto :found_msbuild
    )
)

echo MSBuild not found in common locations.
echo.
echo Please install one of the following:
echo 1. Visual Studio 2017/2019/2022 (any edition)
echo 2. Build Tools for Visual Studio
echo 3. .NET Framework SDK
echo.
echo Download from: https://visualstudio.microsoft.com/downloads/
pause
exit /b 1

:found_msbuild
echo.
echo Checking current PATH...
echo %PATH% | findstr /i "msbuild" >nul
if errorlevel 1 (
    echo MSBuild not in PATH. Adding temporarily...
    set "PATH=%FOUND_MSBUILD%;%PATH%"
    echo MSBuild added to PATH for this session.
) else (
    echo MSBuild already in PATH.
)

echo.
echo Testing MSBuild...
msbuild -version
if errorlevel 1 (
    echo MSBuild test failed!
    pause
    exit /b 1
)

echo.
echo Checking NuGet...
where nuget >nul 2>&1
if errorlevel 1 (
    echo NuGet CLI not found. Downloading...
    powershell -Command "Invoke-WebRequest -Uri 'https://dist.nuget.org/win-x86-commandline/latest/nuget.exe' -OutFile 'nuget.exe'"
    if exist "nuget.exe" (
        echo NuGet downloaded successfully.
        set "PATH=%~dp0;%PATH%"
    ) else (
        echo Failed to download NuGet.
        echo Please download manually from: https://www.nuget.org/downloads
        pause
        exit /b 1
    )
) else (
    echo NuGet CLI found.
)

echo.
echo Checking Squirrel...
where Squirrel >nul 2>&1
if errorlevel 1 (
    echo Squirrel not found. Installing via NuGet...
    if not exist "tools" mkdir "tools"
    nuget install squirrel.windows -OutputDirectory tools -ExcludeVersion
    if exist "tools\squirrel.windows\tools\Squirrel.exe" (
        echo Squirrel installed successfully.
        set "PATH=%~dp0tools\squirrel.windows\tools;%PATH%"
    ) else (
        echo Failed to install Squirrel.
        echo Please install manually: nuget install squirrel.windows
        pause
        exit /b 1
    )
) else (
    echo Squirrel found.
)

echo.
echo ===============================================================
echo                    Setup Complete!
echo ===============================================================
echo.
echo All build tools are ready. You can now run the publish script.
echo.
echo To make PATH changes permanent, add these to your system PATH:
echo - %FOUND_MSBUILD%
if exist "tools\squirrel.windows\tools" echo - %~dp0tools\squirrel.windows\tools
if exist "nuget.exe" echo - %~dp0
echo.

REM 创建环境设置脚本
echo @echo off > SetEnv.bat
echo set "PATH=%FOUND_MSBUILD%;%%PATH%%" >> SetEnv.bat
if exist "tools\squirrel.windows\tools" echo set "PATH=%~dp0tools\squirrel.windows\tools;%%PATH%%" >> SetEnv.bat
if exist "nuget.exe" echo set "PATH=%~dp0;%%PATH%%" >> SetEnv.bat

echo Environment setup script created: SetEnv.bat
echo Run this before using the publish tools.
echo.
pause
